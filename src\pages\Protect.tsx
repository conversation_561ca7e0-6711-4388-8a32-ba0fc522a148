import React from "react";
import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import NewLayout from "@/components/NewLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { ChevronRight, Shield, Zap, Battery, Power, Plug, Lock, Clock, Cpu, Activity, ArrowRight } from "lucide-react";

const protectProducts = [
  {
    id: "ups",
    name: "UPS",
    slug: "ups",
    description: "Backup power systems for critical equipment",
    icon: Battery,
    detailPath: "/protect/ups"
  },
  {
    id: "servo",
    name: "Servo Stabilizers",
    slug: "servo-stabilizers",
    description: "Precise mechanical voltage regulation",
    icon: Power,
    detailPath: "/protect/servo-stabilizers"
  },
  {
    id: "static",
    name: "Static Stabilizers",
    slug: "static-stabilizers",
    description: "Electronic voltage stabilization",
    icon: Zap,
    detailPath: "/protect/static-stabilizers"
  },
  {
    id: "isolation",
    name: "Isolation Transformers",
    slug: "isolation-transformers",
    description: "Protection from electrical noise",
    icon: Shield,
    detailPath: "/protect/isolation-transformers"
  },
];

const Protect = () => {
  // Create groups of 3 products for each section
  const firstRow = protectProducts.slice(0, 3);
  const secondRow = protectProducts.slice(3, 6);
  const thirdRow = protectProducts.slice(6, 9);

  // Enhanced product card component with icons instead of images
  const ProductCard = ({ product }) => (
    <Card className="h-full overflow-hidden bg-white shadow-sm hover:shadow-md transition-all duration-300 border border-blue-100">
      <div className="h-48 overflow-hidden relative bg-blue-50 flex items-center justify-center">
        {product.icon && React.createElement(product.icon, {
          size: 80,
          className: "text-blue-500 transition-transform duration-300 transform hover:scale-110"
        })}
      </div>

      <CardHeader className="pb-0">
        <CardTitle className="text-xl font-bold text-blue-900">{product.name}</CardTitle>
        <CardDescription className="text-blue-700 mt-2">{product.description}</CardDescription>
      </CardHeader>

      <CardFooter className="mt-auto pt-4">
        <Link to={product.detailPath} className="w-full">
          <Button
            className="w-full bg-blue-500 hover:bg-blue-600 text-white rounded-md flex items-center justify-center group"
          >
            <span>Learn More</span>
            <ChevronRight size={16} className="ml-1 transition-transform duration-300 group-hover:translate-x-1" />
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );

  return (
    <NewLayout>
      {/* Container to limit page width */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-8">
        {/* Hero Section - UPDATED based on Measure.tsx reference */}
        <div className="py-16 mb-16">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-8 items-center">
            <div className="md:col-span-7">
              <h1 className="text-3xl md:text-5xl font-bold text-blue-500 mb-4 leading-tight">
                KRYKARD POWER PROTECTION
              </h1>
              <div className="w-24 h-1 bg-blue-400 mb-6"></div>
              <p className="text-blue-800 text-lg max-w-2xl">
                Protect your critical infrastructure with solutions designed to ensure continuous, clean power delivery for all your equipment
              </p>
              <div className="flex flex-wrap gap-4 mt-8">
                <Link to="/contact/sales">
                  <Button className="bg-blue-500 hover:bg-blue-600 text-white rounded-full px-6 py-2 font-medium flex items-center gap-2">
                    <span>Enquiry</span>
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </div>

            <div className="md:col-span-5">
              <div className="relative w-72 h-72 mx-auto">
                {/* Glowing background effect */}
                <div className="absolute inset-0 rounded-full bg-blue-200/30 blur-xl"></div>

                {/* Main center icon with pulsing animation */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 p-5 rounded-full bg-blue-300/40 backdrop-blur-sm animate-pulse">
                  <Shield className="h-20 w-20 text-blue-800 drop-shadow-lg" />
                </div>

                {/* Orbiting icons */}
                <div className="absolute inset-0">
                  {/* Top icon */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-200/40 backdrop-blur-sm rounded-full p-3 shadow-lg">
                    <Battery className="h-10 w-10 text-blue-700" />
                  </div>

                  {/* Right icon */}
                  <div className="absolute top-1/2 right-0 transform translate-x-1/2 -translate-y-1/2 bg-blue-200/40 backdrop-blur-sm rounded-full p-3 shadow-lg">
                    <Zap className="h-10 w-10 text-blue-700" />
                  </div>

                  {/* Bottom icon */}
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 bg-blue-200/40 backdrop-blur-sm rounded-full p-3 shadow-lg">
                    <Power className="h-10 w-10 text-blue-700" />
                  </div>

                  {/* Left icon */}
                  <div className="absolute top-1/2 left-0 transform -translate-x-1/2 -translate-y-1/2 bg-blue-200/40 backdrop-blur-sm rounded-full p-3 shadow-lg">
                    <Plug className="h-10 w-10 text-blue-700" />
                  </div>
                </div>

                {/* Floating small icons with different animation */}
                <div className="absolute top-1/4 right-1/4 bg-blue-200/40 backdrop-blur-sm rounded-full p-2 animate-float">
                  <Clock className="h-6 w-6 text-blue-700" />
                </div>

                <div className="absolute bottom-1/4 left-1/4 bg-blue-200/40 backdrop-blur-sm rounded-full p-2 animate-float" style={{ animationDelay: '2s' }}>
                  <Cpu className="h-6 w-6 text-blue-700" />
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* First Row of Products */}
      {firstRow.length > 0 && (
        <div className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-blue-800">UPS Systems</h2>
            <div className="h-px bg-blue-200 flex-grow ml-6"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {firstRow.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <ProductCard product={product} />
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Second Row of Products - Only show if there are products */}
      {secondRow && secondRow.length > 0 && (
        <div className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-blue-800">Voltage Regulation</h2>
            <div className="h-px bg-blue-200 flex-grow ml-6"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {secondRow.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <ProductCard product={product} />
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Third Row of Products - Only show if there are products */}
      {thirdRow && thirdRow.length > 0 && (
        <div className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-blue-800">Industrial Solutions</h2>
            <div className="h-px bg-blue-200 flex-grow ml-6"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {thirdRow.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <ProductCard product={product} />
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Feature Section */}
      <div className="mb-16 bg-blue-50 rounded-lg p-8 shadow-sm">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-blue-800 mb-3">KRYKARD Power Protection Benefits</h2>
          <p className="text-blue-700 max-w-2xl mx-auto">Our flagship products deliver measurable results for industrial and commercial applications</p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
          {[
            { title: "Fast Response Time", value: "< 10ms protection", icon: Zap },
            { title: "High Efficiency", value: "Up to 98% efficiency", icon: Activity },
            { title: "Smart Monitoring", value: "Remote diagnostics", icon: Cpu },
            { title: "Comprehensive Support", value: "24/7 technical assistance", icon: Clock }
          ].map((feature, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-sm text-center hover:shadow-md transition-all duration-300">
              <div className="flex justify-center mb-3">
                {React.createElement(feature.icon, { className: "h-8 w-8 text-blue-500" })}
              </div>
              <h3 className="text-blue-800 font-bold mb-2">{feature.title}</h3>
              <p className="text-blue-600">{feature.value}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Extended Benefits Section */}
      <div className="mb-16">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold text-blue-800">Key Benefits</h2>
          <div className="h-px bg-blue-200 flex-grow ml-6"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-blue-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-blue-100 rounded-full mr-4">
                <Shield className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-blue-900">Equipment Protection</h3>
            </div>
            <p className="text-blue-700">Safeguard sensitive equipment from power surges, spikes, and outages to extend their operational lifespan.</p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-blue-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-blue-100 rounded-full mr-4">
                <Plug className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-blue-900">Business Continuity</h3>
            </div>
            <p className="text-blue-700">Ensure uninterrupted operations with reliable backup power systems that maintain productivity during outages.</p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-blue-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-blue-100 rounded-full mr-4">
                <Lock className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-blue-900">Data Security</h3>
            </div>
            <p className="text-blue-700">Prevent data loss and system corruption caused by unexpected power failures and electrical disturbances.</p>
          </div>
        </div>
      </div>

      {/* Contact Information Section */}
      <div className="mt-16 mb-16 bg-blue-50 p-10 rounded-lg shadow-sm overflow-hidden relative">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-blue-600 mb-6">Need Expert Advice?</h2>
          <p className="text-blue-700 text-lg mb-8 max-w-2xl mx-auto">
            Our team of experts is ready to help you with product specifications, custom solutions,
            pricing, and any other details you need about KRYKARD Power Protection products.
          </p>
          <div className="flex justify-center">
            <Link to="/contact/sales">
              <Button className="bg-blue-500 hover:bg-blue-600 text-white shadow-md px-8 py-2 text-lg font-medium">
                Contact Our Experts
              </Button>
            </Link>
          </div>
        </div>
      </div>

      </div>
    </NewLayout>
  );
};

export default Protect;