import React, { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import {
  ChevronDown,
  Menu,
  X,
  Phone,
  Mail,
  ArrowUpRight,
  Zap,
  Shield,
  Leaf,
  MessageCircle,
  Building2,
  ChevronRight
} from "lucide-react";

// Custom hook for responsive breakpoints
const useResponsiveBreakpoint = () => {
  const [breakpoint, setBreakpoint] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setBreakpoint('mobile');
      } else if (width < 1024) {
        setBreakpoint('tablet');
      } else {
        setBreakpoint('desktop');
      }
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return breakpoint;
};

// Modern Dropdown Design with your new clean styling
const MegaDropdown = ({
  items,
  isOpen,
  color,
  category,
  onItemClick
}: {
  items: { name: string; path: string }[],
  isOpen: boolean,
  color: string,
  category: string,
  onItemClick?: () => void
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [shouldShow, setShouldShow] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const breakpoint = useResponsiveBreakpoint();

  useEffect(() => {
    if (isOpen) {
      setShouldShow(true);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    } else if (!isHovered) {
      timeoutRef.current = setTimeout(() => {
        setShouldShow(false);
      }, 150);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isOpen, isHovered]);

  if (!shouldShow || breakpoint === 'mobile') return null;

  const getColorConfig = () => {
    switch (color) {
      case 'measure':
        return {
          bg: 'bg-blue-600',
          lightBg: 'bg-blue-50',
          text: 'text-blue-600',
          border: 'border-blue-200'
        };
      case 'protect':
        return {
          bg: 'bg-emerald-600',
          lightBg: 'bg-emerald-50',
          text: 'text-emerald-600',
          border: 'border-emerald-200'
        };
      case 'conserve':
        return {
          bg: 'bg-purple-600',
          lightBg: 'bg-purple-50',
          text: 'text-purple-600',
          border: 'border-purple-200'
        };
      case 'about':
        return {
          bg: 'bg-gray-600',
          lightBg: 'bg-gray-50',
          text: 'text-gray-600',
          border: 'border-gray-200'
        };
      case 'contact':
        return {
          bg: 'bg-indigo-600',
          lightBg: 'bg-indigo-50',
          text: 'text-indigo-600',
          border: 'border-indigo-200'
        };
      default:
        return {
          bg: 'bg-gray-600',
          lightBg: 'bg-gray-50',
          text: 'text-gray-600',
          border: 'border-gray-200'
        };
    }
  };

  const colorConfig = getColorConfig();
  const isMeasureCategory = category === 'measure';

  return (
    <motion.div
      className={cn(
        "absolute top-full left-1/2 transform -translate-x-1/2 mt-2",
        "bg-white/95 backdrop-blur-md border border-gray-200 rounded-lg shadow-lg",
        "overflow-hidden z-50",
        isMeasureCategory ? "w-[480px]" : "w-[320px]"
      )}
      initial={{ opacity: 0, y: -10, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -10, scale: 0.95 }}
      transition={{ duration: 0.2, ease: [0.23, 1, 0.32, 1] }}
      onMouseEnter={() => {
        setIsHovered(true);
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      }}
      onMouseLeave={() => {
        setIsHovered(false);
      }}
    >
      {/* Header */}
      <div className={cn("px-4 py-3 border-b", colorConfig.lightBg, colorConfig.border)}>
        <h3 className={cn("font-medium text-base", colorConfig.text)}>
          {category.charAt(0).toUpperCase() + category.slice(1)}
        </h3>
      </div>

      {/* Menu Items */}
      <div className="py-2">
        <div className={isMeasureCategory ? "grid grid-cols-2" : ""}>
          {items.map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.1, delay: index * 0.02 }}
            >
              <Link
                to={item.path}
                className={cn(
                  "flex items-center justify-between px-4 py-3",
                  "text-gray-700 hover:text-gray-900",
                  "transition-all duration-200 group",
                  "hover:bg-gray-50",
                  "border-b border-gray-50 last:border-b-0"
                )}
                onClick={onItemClick}
              >
                <span className="text-sm font-medium flex-1 pr-2">
                  {item.name}
                </span>
                <ChevronRight className="w-4 h-4 text-gray-400 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-200" />
              </Link>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Footer Button */}
      <div className="p-3 border-t border-gray-100">
        <Link
          to={`/${category}`}
          className={cn(
            "w-full flex items-center justify-center gap-2 px-4 py-2.5",
            "rounded-lg text-white font-medium text-sm",
            "transition-all duration-200 hover:shadow-md",
            colorConfig.bg
          )}
          onClick={onItemClick}
        >
          <span>View All Products</span>
          <ArrowUpRight className="w-4 h-4" />
        </Link>
      </div>
    </motion.div>
  );
};

const Navigation: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [isScrolled, setIsScrolled] = useState(false);
  const breakpoint = useResponsiveBreakpoint();

  // Handle scroll
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    
    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Close mobile menu when resizing to desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsMenuOpen(false);
        setActiveDropdown(null);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Close dropdown when switching to mobile
  useEffect(() => {
    if (breakpoint === 'mobile') {
      setActiveDropdown(null);
    }
  }, [breakpoint]);

  // Lock body scroll when mobile menu is open
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isMenuOpen]);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleMouseEnter = (dropdown: string) => {
    if (breakpoint !== 'mobile') {
      setActiveDropdown(dropdown);
    }
  };

  const handleMouseLeave = () => {
    if (breakpoint !== 'mobile') {
      setActiveDropdown(null);
    }
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });

    // Close mobile menu if open
    if (isMenuOpen) {
      setIsMenuOpen(false);
      document.body.style.overflow = '';
    }
  };

  // Menu categories with modern structure
  const menuCategories = {
    measure: {
      name: "Measure",
      icon: Zap,
      items: [
        { name: "Power Quality Analyzers", path: "/measure/power-quality-analyzers" },
        { name: "Thermal Imagers", path: "/measure/thermal-imagers" },
        { name: "Insulation Testers", path: "/measure/insulation-testers" },
        { name: "Oscilloscopes", path: "/measure/oscilloscopes" },
        { name: "Earth Testers", path: "/measure/earthtesters" }, // Fixed route
        { name: "Earth Loop Testers", path: "/measure/earth-loop-testers" },
        { name: "Clamp Meters", path: "/measure/clamp-meters" },
        { name: "Digital Multimeters", path: "/measure/digital-multimeters" },
        { name: "Micro Ohm Meters", path: "/measure/micro-ohmmeters" },
        { name: "Installation Testers", path: "/measure/installation-testers" },
        { name: "Multi Functional Meters", path: "/measure/multi-functional-meters" },
      ]
    },
    protect: {
      name: "Protect",
      icon: Shield,
      items: [
        { name: "Online UPS", path: "/protect/ups" },
        { name: "Servo Stabilizers", path: "/protect/servo-stabilizers" },
        { name: "Static Stabilizers", path: "/protect/static-stabilizers" },
        { name: "Isolation Transformers", path: "/protect/isolation-transformers" },
      ]
    },
    conserve: {
      name: "Conserve",
      icon: Leaf,
      items: [
        { name: "Smart Energy Management System", path: "/conserve/on-premise-systems" },
        { name: "Smart Factory Solution", path: "/conserve/smart-factory-solution" },
        { name: "Tenant Billing Solution", path: "/conserve/tenant-billing-solution" },
        { name: "Enterprise ESG Reporting", path: "/conserve/enterprise-esg-reporting" },
      ]
    },
    about: {
      name: "About Us",
      icon: Building2,
      items: [
        { name: "Company", path: "/about/company" },
        { name: "Vision & Mission", path: "/about/vision-mission" },
        { name: "Infrastructure & Our Network", path: "/about/infrastructure-network" },
        { name: "Certificates", path: "/about/certificates" },
        { name: "Events", path: "/about/events" },
      ]
    },
    contact: {
      name: "Contact",
      icon: MessageCircle,
      items: [
        { name: "Contact Sales", path: "/contact/sales" },
        { name: "Technical Services", path: "/contact/service" },
      ]
    }
  };

  return (
    <header
      className={cn(
        "fixed top-0 left-0 right-0 z-50 py-2 sm:py-3 md:py-4 transition-all duration-300",
        isScrolled
          ? "bg-white/80 backdrop-blur-md shadow-sm"
          : "bg-transparent"
      )}
    >
      <div className="container flex items-center justify-between px-4 sm:px-6 lg:px-8">
        {/* Logo */}
        <Link
          to="/"
          className="flex items-center space-x-2"
          onClick={(e) => {
            e.preventDefault();
            scrollToTop();
          }}
          aria-label="KRYKARD"
        >
          <img
            src="/unnamed.png"
            alt="KRYKARD Logo"
            className="h-8 sm:h-10 w-auto object-contain"
          />
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden lg:flex items-center space-x-8">
          {Object.entries(menuCategories).map(([key, category]) => (
            <div
              key={key}
              className="relative"
              onMouseEnter={() => handleMouseEnter(key)}
              onMouseLeave={handleMouseLeave}
            >
              <Link
                to={`/${key}`}
                className={cn(
                  "nav-link flex items-center font-medium transition-all duration-200",
                  "hover:text-gray-900"
                )}
              >
                <span className="relative z-10">{category.name}</span>
                <ChevronDown className={cn(
                  "ml-2 h-4 w-4 transition-transform duration-200",
                  activeDropdown === key ? "rotate-180" : ""
                )} />
              </Link>

              <MegaDropdown
                items={category.items}
                isOpen={activeDropdown === key}
                color={key}
                category={key}
                onItemClick={() => setActiveDropdown(null)}
              />
            </div>
          ))}
        </nav>

        {/* Contact Info - Desktop Only */}
        <div className="hidden xl:flex items-center space-x-6">
          <a
            href="tel:+919500097966"
            className="flex items-center space-x-2 text-gray-700 hover:text-gray-900 transition-colors"
          >
            <Phone className="h-4 w-4" />
            <span className="text-sm font-medium">+91 95000 97966</span>
          </a>
          <a
            href="mailto:<EMAIL>"
            className="flex items-center space-x-2 text-gray-700 hover:text-gray-900 transition-colors"
          >
            <Mail className="h-4 w-4" />
            <span className="text-sm font-medium"><EMAIL></span>
          </a>
        </div>

        {/* Mobile menu button */}
        <button
          className="lg:hidden text-gray-700 p-3 focus:outline-none"
          onClick={toggleMenu}
          aria-label={isMenuOpen ? "Close menu" : "Open menu"}
        >
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, x: "100%" }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: "100%" }}
            transition={{ type: "spring", damping: 25, stiffness: 200 }}
            className="fixed inset-0 z-40 bg-white flex flex-col pt-16 px-6 lg:hidden"
          >
            <nav className="flex flex-col space-y-6 mt-8">
              {Object.entries(menuCategories).map(([key, category]) => (
                <div key={key} className="space-y-3">
                  <Link
                    to={`/${key}`}
                    className="text-xl font-medium py-3 px-6 w-full text-center rounded-lg hover:bg-gray-100 block"
                    onClick={() => {
                      setIsMenuOpen(false);
                      document.body.style.overflow = '';
                    }}
                  >
                    {category.name}
                  </Link>
                  <div className="space-y-2 ml-4">
                    {category.items.map((item, idx) => (
                      <Link
                        key={idx}
                        to={item.path}
                        className="block text-gray-600 hover:text-gray-900 transition-colors py-2 text-sm"
                        onClick={() => {
                          setIsMenuOpen(false);
                          document.body.style.overflow = '';
                        }}
                      >
                        {item.name}
                      </Link>
                    ))}
                  </div>
                </div>
              ))}
            </nav>

            {/* Contact Footer */}
            <div className="mt-auto pb-8 space-y-4">
              <a
                href="tel:+919500097966"
                className="flex items-center justify-center gap-3 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-100"
              >
                <Phone className="w-5 h-5" />
                <span className="font-medium">+91 95000 97966</span>
              </a>
              <a
                href="mailto:<EMAIL>"
                className="flex items-center justify-center gap-3 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-100"
              >
                <Mail className="w-5 h-5" />
                <span className="font-medium"><EMAIL></span>
              </a>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
};

export default Navigation;
