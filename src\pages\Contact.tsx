import { useRef } from "react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import EnhancedLayout from "@/components/EnhancedLayout";
import { Button } from "@/components/ui/button";
import { Mail, Phone, MapPin, Wrench, ArrowRight, Headset, Clock } from "lucide-react";

const contactSections = [
  {
    name: "Sales",
    slug: "sales",
    description: "Product information and tailored solutions for your energy management needs",
    icon: Mail,
    color: "blue",
    bgColor: "bg-blue-50",
    textColor: "text-blue-600",
    btnColor: "bg-blue-600 hover:bg-blue-700",
    contactInfo: "<EMAIL>"
  },
  {
    name: "Technical Service",
    slug: "service",
    description: "24/7 technical assistance and support for all our products",
    icon: Wrench,
    color: "green",
    bgColor: "bg-green-50",
    textColor: "text-green-600",
    btnColor: "bg-green-600 hover:bg-green-700",
    contactInfo: "+91 95000 97966"
  },
];

const Contact = () => {
  // References for scroll animations
  const heroRef = useRef(null);
  
  return (
    <EnhancedLayout
      title="Contact KRYKARD"
      subtitle="Get in touch with our team of energy management experts"
      category="contact"
    >
      {/* Hero Section */}
      <div className="w-full py-16 mb-16" ref={heroRef}>
        <div className="max-w-6xl mx-auto px-4 sm:px-6">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-8 items-center">
            <div className="md:col-span-7">
              <h1 className="text-3xl md:text-5xl font-bold text-blue-600 mb-4 leading-tight tracking-tight">
                GET IN TOUCH WITH OUR EXPERTS
              </h1>
              <p className="text-gray-700 text-lg max-w-2xl leading-relaxed">
                At KRYKARD, we're committed to providing exceptional support and assistance. Whether you have questions about our products, need technical support, or want to explore partnership opportunities, our team is here to help.
              </p>

              <div className="mt-8 space-y-4">
                <motion.div 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4 }}
                  className="flex items-center"
                >
                  <Mail className="h-6 w-6 mr-4 text-blue-600" />
                  <span className="text-gray-700 font-medium"><EMAIL></span>
                </motion.div>
                <motion.div 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: 0.1 }}
                  className="flex items-center"
                >
                  <Phone className="h-6 w-6 mr-4 text-green-600" />
                  <span className="text-gray-700 font-medium">+91 95000 97966</span>
                </motion.div>
                <motion.div 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: 0.2 }}
                  className="flex items-center"
                >
                  <MapPin className="h-6 w-6 mr-4 text-yellow-500" />
                  <span className="text-gray-700 font-medium">No.5, Kumaran St, Pazhvanthangal, Chennai, Tamil Nadu, India, 600114</span>
                </motion.div>
              </div>

              <div className="flex flex-wrap gap-4 mt-8">
                <Link to="/contact/sales">
                  <Button className="bg-blue-600 text-white hover:bg-blue-700 font-medium flex items-center group">
                    Contact Sales <ArrowRight className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                  </Button>
                </Link>
                <Link to="/contact/service">
                  <Button variant="outline" className="text-green-600 border-green-500 hover:bg-green-50 font-medium group">
                    Technical Support <ArrowRight className="ml-2 h-5 w-5 opacity-0 transition-all duration-300 group-hover:opacity-100 group-hover:translate-x-1" />
                  </Button>
                </Link>
              </div>
            </div>
            <div className="md:col-span-5 hidden md:block">
              <div className="relative bg-yellow-50 p-6 rounded-lg">
                {/* Business hours content */}
                <motion.div 
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.8 }}
                >
                  <h3 className="text-2xl font-bold text-yellow-600 mb-6">Business Hours</h3>

                  <div className="space-y-6">
                    <motion.div 
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5 }}
                      className="flex items-start"
                    >
                      <Headset className="h-6 w-6 mr-4 text-blue-600" />
                      <div>
                        <h4 className="text-lg font-semibold text-blue-600 mb-1">Customer Support</h4>
                        <p className="text-gray-700">24/7 assistance for urgent technical issues</p>
                      </div>
                    </motion.div>

                    <motion.div 
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.1 }}
                      className="flex items-start"
                    >
                      <Clock className="h-6 w-6 mr-4 text-green-600" />
                      <div>
                        <h4 className="text-lg font-semibold text-green-600 mb-1">Office Hours</h4>
                        <p className="text-gray-700">Monday - Friday: 9:00 AM - 6:00 PM IST</p>
                        <p className="text-gray-700">Saturday: 9:00 AM - 1:00 PM IST</p>
                        <p className="text-gray-700">Sunday: Closed</p>
                      </div>
                    </motion.div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Departments Section */}
      <div className="mb-16">
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-yellow-600">Need More Information?</h2>
            <p className="text-gray-700 mb-8 text-lg max-w-3xl mx-auto">
              Our team of experts is ready to help you with product specifications, custom solutions,
              pricing, and any other details you need about the KRYKARD Static Voltage Regulator.
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {contactSections.map((section, index) => {
            const Icon = section.icon;
            
            return (
              <motion.div 
                key={section.slug}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                className={`transform transition-transform duration-300 ${section.bgColor} p-6 rounded-lg`}
              >
                <div className="flex flex-col h-full">
                  <div className="flex items-center mb-4">
                    <Icon className={`h-6 w-6 mr-4 ${section.textColor}`} />
                    <h3 className={`text-xl font-bold ${section.textColor}`}>{section.name}</h3>
                  </div>

                  <p className="mb-6 text-gray-700 text-lg flex-grow">
                    {section.description}
                  </p>

                  <div className="mb-6">
                    <div className="font-medium text-lg flex items-center text-gray-700">
                      {section.name === "Sales" ? 
                        <Mail className={`h-5 w-5 mr-3 ${section.textColor}`} /> : 
                        <Phone className={`h-5 w-5 mr-3 ${section.textColor}`} />
                      }
                      {section.contactInfo}
                    </div>
                  </div>

                  <Link to={`/contact/${section.slug}`} className="inline-block">
                    <Button className={`${section.btnColor} text-white font-medium flex items-center group shadow-md`}>
                      Contact {section.name}
                      <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                    </Button>
                  </Link>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Call to Action Section */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, amount: 0.3 }}
        transition={{ duration: 0.6 }}
        className="mt-16 mb-16 bg-yellow-50 py-12 px-8 rounded-lg"
      >
        <div className="text-center relative z-10">
          <h2 className="text-3xl font-bold text-yellow-600 mb-6">Need More Information?</h2>
          <p className="text-gray-700 text-lg mb-8 mx-auto max-w-2xl">
            Our team of experts is ready to help you with product specifications, custom solutions,
            pricing, and any other details you need about the KRYKARD Static Voltage Regulator.
          </p>
          <div className="flex justify-center">
            <Link to="/contact/sales">
              <Button className="bg-blue-600 text-white hover:bg-blue-700 px-8 py-2 text-lg font-medium group flex items-center shadow-md">
                Contact Our Experts
                <ArrowRight className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
              </Button>
            </Link>
          </div>
        </div>
      </motion.div>
    </EnhancedLayout>
  );
};

export default Contact;