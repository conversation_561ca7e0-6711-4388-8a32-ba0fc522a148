import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import ChatBotProvider from "./components/ChatBotProvider";
import ScrollToTop from "@/components/ScrollToTop"; // Import the ScrollToTop component


// Admin pages
import Login from "./pages/admin/Login";
import Dashboard from "./pages/admin/Dashboard";
import PopupManager from "./pages/admin/PopupManager";
import UserSubmissions from "./pages/admin/UserSubmissions";
import CreateAdmin from "./pages/admin/CreateAdmin";
import TestFirestore from "./pages/admin/TestFirestore";
import DiagnosticPage from "./pages/admin/DiagnosticPage";
import ChatDetails from "./pages/admin/ChatDetails";
import TestPopupData from "./components/TestPopupData";

// Main category pages
import Measure from "./pages/Measure";
import Protect from "./pages/Protect";
import Conserve from "./pages/Conserve";
import About from "./pages/About";
import Contact from "./pages/Contact";

// Contact subpages
import Sales from "./pages/contact/Sales";
import Service from "./pages/contact/Service";


// Measure subpages
import PowerQualityAnalyzers from "./pages/measure/PowerQualityAnalyzers";
import InsulationTesters from "./pages/measure/InsulationTesters";
import Oscilloscopes from "./pages/measure/Oscilloscopes";
import EarthLoopTesters from "./pages/measure/EarthLoopTester";
import DigitalMultimeters from "./pages/measure/Multimeters";
import ClampMeters from "./pages/measure/clampmeters";
import EarthTesters from "./pages/measure/EarthTesters";
import MicroOhmmeters from "./pages/measure/MicroOhmmeters";
import MultiFunctionalMeters from "./pages/measure/MultiFunctionalMeters";
import ThermalImagers from "./pages/measure/ThermalImager";
import InstallationTesters from "./pages/measure/InstallationTesters";

import ThermalImagersSpecification from "./pages/measure/productpages/ThermalImagersSpecification";
import MicroOhmMeterProduct from "./pages/measure/productpages/MicroOhmMeterproduct";

// Protect subpages
import UPS from "./pages/protect/UPS";
import ServoStabilizers from "./pages/protect/ServoStabilizers";
import StaticStabilizers from "./pages/protect/StaticStabilizers";
import IsolationTransformers from "./pages/protect/IsolationTransformers";
import IsolationTransformersProduct from "./pages/protect/productpages/isolationtransformersproduct";
import UltraIsolationTransformer from "./pages/protect/productpages/UltraIsolationTransformer";
import GalvanicIsolationTransformer from "./pages/protect/productpages/GalvanicIsolationTransformer";
import VoltageRegulatorProduct from "./pages/protect/productpages/Staticstabilizersproduct";
import SinglePhaseStabilizer from "./pages/protect/productpages/Servostabilizersphase1";
import ThreePhaseStabilizer from "./pages/protect/productpages/Servostabilizers3phase";

// Conserve subpages
import OnPremiseSystems from "./pages/conserve/OnPremiseSystems";
import SmartFactorySolution from "./pages/conserve/SmartFactorySolution";
import LightingEnergySaver from "./pages/conserve/LightingEnergySaver";
import EnergyAudits from "./pages/conserve/EnergyAudits";


// About subpages
import Company from "./pages/about/Company";
import Certificates from "./pages/about/Certificates";
// import Sustainability from "./pages/about/Sustainability";
import Events from "./pages/about/events";
// import Partners from "./pages/about/Partners";
// import Resources from "./pages/about/Resources";
import VisionAndMission from "./pages/about/VisionAndMission";
import InfrastructureAndNetwork from "./pages/about/InfrastructureAndNetwork";

// New UPS Series Product Specification Pages
import ELSeriesUPS from "./pages/protect/productpages/EL/ELBSeriesUPS"; // EL/ELB Series
import EH11SeriesUPS from "./pages/protect/productpages/EH11SeriesUPS"; // EH 11 Series
import EH31SeriesUPS from "./pages/protect/productpages/EH31SeriesUPS"; // EH 31 Series
import EH33SmallSeriesUPS from "./pages/protect/productpages/EH33SmallSeriesUPS"; // EH 33 Series (10-60 kVA)
import EH33LargeSeriesUPS from "./pages/protect/productpages/EH33LargeSeriesUPS"; // EH 33 Series (80-200 kVA)
import SXSeriesUPS from "./pages/protect/productpages/SXSeriesUPS"; // SX Series
import HXSeriesUPS from "./pages/protect/productpages/HXSeriesUPS"; // HX Series

// UPS Series Overview Pages
import ELSeriesOverview from "./pages/protect/productpages/overview/ELSeriesOverview"; // EL/ELB Series Overview
import EH11SeriesOverview from "./pages/protect/productpages/overview/EH11SeriesOverview"; // EH 11 Series Overview
import EH31SeriesOverview from "./pages/protect/productpages/overview/EH31SeriesOverview"; // EH 31 Series Overview
import EH33SmallSeriesOverview from "./pages/protect/productpages/overview/EH33SmallSeriesOverview"; // EH 33 Small Series Overview
import EH33LargeSeriesOverview from "./pages/protect/productpages/overview/EH33LargeSeriesOverview"; // EH 33 Large Series Overview
import SXSeriesOverview from "./pages/protect/productpages/overview/SXSeriesOverview"; // SX Series Overview
import HXSeriesOverview from "./pages/protect/productpages/overview/HXSeriesOverview"; // HX Series Overview
import ErrorBoundary from "./components/ErrorBoundary";
import { AuthProvider } from "./contexts/AuthContext";
import TestPage from "./pages/TestPage";

const queryClient = new QueryClient();

const App = () => {
  console.log('🎯 App component rendering...');

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter
            future={{
              v7_startTransition: true,
              v7_relativeSplatPath: true,
            }}
          >
            <ScrollToTop /> {/* Add ScrollToTop component here */}
            <AuthProvider >
            <ChatBotProvider>
              <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/test" element={<TestPage />} />
            {/* Admin Routes */}
              <Route path="/admin/login" element={<Login />} />
              <Route path="/admin/create" element={<CreateAdmin />} />
              <Route path="/admin/test-firestore" element={<TestFirestore />} />
              <Route path="/admin/diagnostic" element={<DiagnosticPage />} />
              <Route path="/admin/test-popup" element={<TestPopupData />} />
              <Route path="/admin" element={<Dashboard />}>
                <Route path="dashboard" element={<PopupManager />} />
                <Route path="popups" element={<PopupManager />} />
                <Route path="users" element={<UserSubmissions />} />
                <Route path="chat/:id" element={<ChatDetails />} />
              </Route>

            {/* Main category pages */}
            <Route path="/measure" element={<Measure />} />
            <Route path="/protect" element={<Protect />} />
            <Route path="/conserve" element={<Conserve />} />
            <Route path="/about" element={<About />} />
            <Route path="/contact" element={<Contact />} />

            {/* Contact subpages */}
            <Route path="/contact/sales" element={<Sales />} />
            <Route path="/contact/service" element={<Service />} />

            {/* Measure subpages */}
            <Route path="/measure/power-quality-analyzers" element={<PowerQualityAnalyzers />} />
            <Route path="/measure/thermal-imagers" element={<ThermalImagers />} />
            <Route path="/measure/insulation-testers" element={<InsulationTesters />} />
            <Route path="/measure/earth-loop-testers" element={<EarthLoopTesters />} />
            <Route path="/measure/digital-multimeters" element={<DigitalMultimeters />} />
            <Route path="/measure/clamp-meters" element={<ClampMeters />} />
            <Route path="/measure/earthtesters" element={<EarthTesters />} />
            <Route path="/measure/micro-ohmmeters" element={<MicroOhmmeters />} />
            <Route path="/measure/multi-functional-meters" element={<MultiFunctionalMeters />} />
            <Route path="/measure/thermal-imagers" element={<ThermalImagers />} />
            <Route path="/measure/installation-testers" element={<InstallationTesters />} />
            <Route path="/measure/oscilloscopes" element={<Oscilloscopes />} />

            <Route path="/measure/productpages/thermal-imagers/specification" element={<ThermalImagersSpecification />} />
            <Route path="/measure/micro-ohmmeters/product/:productId" element={<MicroOhmMeterProduct />} />

            {/* Protect subpages */}
            <Route path="/protect/ups" element={<UPS />} />
            <Route path="/protect/servo-stabilizers" element={<ServoStabilizers />} />
            <Route path="/protect/productpages/SinglePhaseStabilizer" element={<SinglePhaseStabilizer />} />
            <Route path="/protect/productpages/ThreePhaseStabilizer" element={<ThreePhaseStabilizer />} />
            <Route path="/protect/static-stabilizers" element={<StaticStabilizers />} />
            <Route path="/protect/static-stabilizers/product" element={<VoltageRegulatorProduct />} />
            <Route path="/protect/isolation-transformers" element={<IsolationTransformers />} />
            <Route path="/protect/isolation-transformers/:type" element={<IsolationTransformersProduct />} />
            <Route path="/protect/isolation-transformers/ultra-isolation-transformer" element={<UltraIsolationTransformer />} />
            <Route path="/protect/isolation-transformers/galvanic-isolation-transformer" element={<GalvanicIsolationTransformer />} />
            

            {/* UPS Series Pages - Using the new dedicated product pages */}
            <Route path="/protect/ups/el-series" element={<ELSeriesUPS />} />
            <Route path="/protect/ups/eh-11-series" element={<EH11SeriesUPS />} />
            <Route path="/protect/ups/eh-31-series" element={<EH31SeriesUPS />} />
            <Route path="/protect/ups/eh-33-small-series" element={<EH33SmallSeriesUPS />} />
            <Route path="/protect/ups/eh-33-large-series" element={<EH33LargeSeriesUPS />} />
            <Route path="/protect/ups/sx-series" element={<SXSeriesUPS />} />
            <Route path="/protect/ups/hx-series" element={<HXSeriesUPS />} />

            {/* UPS Series Overview Pages */}
            <Route path="/protect/ups/el-series/overview" element={<ELSeriesOverview />} />
            <Route path="/protect/ups/eh-11-series/overview" element={<EH11SeriesOverview />} />
            <Route path="/protect/ups/eh-31-series/overview" element={<EH31SeriesOverview />} />
            <Route path="/protect/ups/eh-33-small-series/overview" element={<EH33SmallSeriesOverview />} />
            <Route path="/protect/ups/eh-33-large-series/overview" element={<EH33LargeSeriesOverview />} />
            <Route path="/protect/ups/sx-series/overview" element={<SXSeriesOverview />} />
            <Route path="/protect/ups/hx-series/overview" element={<HXSeriesOverview />} />

            {/* Conserve subpages */}
            <Route path="/conserve/on-premise-systems" element={<OnPremiseSystems />} />
            <Route path="/conserve/smart-factory-solution" element={<SmartFactorySolution />} />
            <Route path="/conserve/tenant-billing-solution" element={<LightingEnergySaver />} />
            <Route path="/conserve/enterprise-esg-reporting" element={<EnergyAudits />} />

            {/* About subpages */}
            <Route path="/about/company" element={<Company />} />
            <Route path="/about/certificates" element={<Certificates />} />
            {/* <Route path="/about/sustainability" element={<Sustainability />} /> */}
            <Route path="/about/events" element={<Events />} />
            <Route path="/about/vision-mission" element={<VisionAndMission />} />
            <Route path="/about/infrastructure-network" element={<InfrastructureAndNetwork />} />
            {/* <Route path="/about/partners" element={<Partners />} />
            <Route path="/about/resources" element={<Resources />} /> */}

            {/* Catch-all route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
            </ChatBotProvider>
            </AuthProvider>
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;