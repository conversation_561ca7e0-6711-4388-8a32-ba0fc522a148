import React from "react";
import Navbar from "./Navbar";
import ModernFooter from "./layout/FooterComponents";
import ChatBot from "./Chatbot";
import ScrollToTop from "./ScrollToTop";

interface EnhancedLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  category?: "measure" | "protect" | "conserve" | "about" | "contact";
  showHero?: boolean;
}

// Get theme colors based on category
const getThemeColors = (category?: string) => {
  switch (category) {
    case "measure":
      return {
        primary: "text-yellow-500",
        secondary: "text-yellow-800", 
        accent: "bg-yellow-400",
        background: "bg-gradient-to-br from-yellow-50 via-white to-amber-50"
      };
    case "protect":
      return {
        primary: "text-blue-500",
        secondary: "text-blue-800",
        accent: "bg-blue-400", 
        background: "bg-gradient-to-br from-blue-50 via-white to-indigo-50"
      };
    case "conserve":
      return {
        primary: "text-green-500",
        secondary: "text-green-800",
        accent: "bg-green-400",
        background: "bg-gradient-to-br from-green-50 via-white to-emerald-50"
      };
    case "about":
      return {
        primary: "text-purple-500", 
        secondary: "text-purple-800",
        accent: "bg-purple-400",
        background: "bg-gradient-to-br from-purple-50 via-white to-violet-50"
      };
    case "contact":
      return {
        primary: "text-indigo-500",
        secondary: "text-indigo-800", 
        accent: "bg-indigo-400",
        background: "bg-gradient-to-br from-indigo-50 via-white to-blue-50"
      };
    default:
      return {
        primary: "text-gray-900",
        secondary: "text-gray-700",
        accent: "bg-gray-400",
        background: "bg-gradient-to-br from-gray-50 via-white to-slate-50"
      };
  }
};

const EnhancedLayout: React.FC<EnhancedLayoutProps> = ({ 
  children, 
  title, 
  subtitle, 
  category,
  showHero = true 
}) => {
  const theme = getThemeColors(category);

  return (
    <div className="min-h-screen bg-white text-gray-900 font-sans">
      {/* Your Beautiful Navbar */}
      <Navbar />
      
      {/* Main content with proper spacing for fixed navbar */}
      <main className="pt-20 sm:pt-24">
        {/* Hero Section - Only show if title is provided and showHero is true */}
        {showHero && title && (
          <div className={`relative py-16 mb-8 ${theme.background}`}>
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center">
                <h1 className={`text-3xl md:text-5xl font-bold ${theme.primary} mb-4 leading-tight`}>
                  {title}
                </h1>
                <div className={`w-24 h-1 ${theme.accent} mx-auto mb-6`}></div>
                {subtitle && (
                  <p className={`${theme.secondary} text-lg max-w-3xl mx-auto`}>
                    {subtitle}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}
        
        {/* Page Content */}
        <div className="relative">
          {children}
        </div>
      </main>
      
      {/* Scroll to top button */}
      <ScrollToTop />
      
      {/* Chat Bot */}
      <ChatBot />
      
      {/* Footer */}
      <ModernFooter />
    </div>
  );
};

export default EnhancedLayout;
