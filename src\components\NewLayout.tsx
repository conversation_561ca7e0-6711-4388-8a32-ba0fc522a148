import React from "react";
import Navbar from "./Navbar";
import ModernFooter from "./layout/FooterComponents";
import ChatBot from "./Chatbot";
import ScrollToTop from "./ScrollToTop";

interface NewLayoutProps {
  children: React.ReactNode;
}

const NewLayout: React.FC<NewLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-white text-gray-900 font-sans">
      {/* New Beautiful Navbar */}
      <Navbar />
      
      {/* Main content with proper spacing for fixed navbar */}
      <main className="pt-20 sm:pt-24">
        {children}
      </main>
      
      {/* Scroll to top button */}
      <ScrollToTop />
      
      {/* Chat Bot */}
      <ChatBot />
      
      {/* Footer */}
      <ModernFooter />
    </div>
  );
};

export default NewLayout;
