   import React from "react";
import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import NewLayout from "@/components/NewLayout";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { ChevronRight, ArrowRight, Gauge, Thermometer, Shield, Activity, Zap, Cpu, Clock, Ruler, Compass, Maximize, Minimize, BarChart2, Sliders } from "lucide-react";

const measureProducts = [
  {
    id: "power-analyzers",
    name: "Power Quality Analyzers",
    slug: "power-quality-analyzers",
    description: "Monitor and record power quality parameters",
    icon: Activity,
    detailPath: "/measure/power-quality-analyzers",
    features: [
      "Harmonic analysis up to the 50th order",
      "Real-time power factor monitoring",
      "Voltage sag and swell detection"
    ]
  },
  {
    id: "thermal",
    name: "Thermal Imagers",
    slug: "thermal-imagers",
    description: "Detect thermal anomalies in electrical systems",
    icon: Thermometer,
    detailPath: "/measure/thermal-imagers",
    features: [
      "High-resolution infrared imaging",
      "Temperature range from -20°C to 650°C",
      "Automatic hot spot detection"
    ]
  },
  {
    id: "insulation",
    name: "Insulation Testers",
    slug: "insulation-testers",
    description: "Test insulation resistance in electrical systems",
    icon: Shield,
    detailPath: "/measure/insulation-testers",
    features: [
      "Test voltages from 50V to 5000V",
      "Polarization index measurement",
      "Automatic discharge after testing"
    ]
  },
  {
    id: "oscilloscopes",
    name: "Oscilloscopes",
    slug: "oscilloscopes",
    description: "Visualize electrical signals in real-time",
    icon: Zap,
    detailPath: "/measure/oscilloscopes",
    features: [
      "Bandwidth up to 200 MHz",
      "Advanced triggering capabilities",
      "Waveform capture and analysis"
    ]
  },
  {
    id: "earth",
    name: "Earth Testers",
    slug: "earth-testers",
    description: "Measure earth resistance in electrical systems",
    icon: Compass,
    detailPath: "/measure/earth-testers",
    features: [
      "3-pole and 4-pole measurement methods",
      "Soil resistivity testing",
      "Stakeless measurement capability"
    ]
  },
  {
    id: "earth-loop",
    name: "Earth Loop Testers",
    slug: "earth-loop-testers",
    description: "Measure earth loop resistance in electrical systems",
    icon: Maximize,
    detailPath: "/measure/earth-loop-testers",
    features: [
      "High-current loop testing",
      "RCD trip avoidance technology",
      "Prospective fault current calculation"
    ]
  },
  {
    id: "clamp",
    name: "Clamp Meters",
    slug: "clamp-meters",
    description: "Measure current and voltage in electrical systems",
    icon: Minimize,
    detailPath: "/measure/clamp-meters",
    features: [
      "AC/DC current measurement up to 1000A",
      "True RMS measurement capability",
      "Inrush current detection"
    ]
  },
  {
    id: "multimeters",
    name: "Digital Multimeters",
    slug: "digital-multimeters",
    description: "Measure voltage, current, and power in electrical systems",
    icon: Cpu,
    detailPath: "/measure/digital-multimeters",
    features: [
      "CAT IV 600V safety rating",
      "Low impedance voltage measurement",
      "Frequency and capacitance testing"
    ]
  },
  {
    id: "micro-ohmmeters",
    name: "Micro-ohmmeters",
    slug: "micro-ohmmeters",
    description: "Measure resistance in electrical systems",
    icon: Gauge,
    detailPath: "/measure/micro-ohmmeters",
    features: [
      "Resolution down to 0.1 μΩ",
      "Test currents up to 100A",
      "Temperature compensation"
    ]
  },
  {
    id: "installation",
    name: "Installation Testers",
    slug: "installation-testers",
    description: "Measure resistance in electrical systems",
    icon: Sliders,
    detailPath: "/measure/installation-testers",
    features: [
      "All-in-one electrical installation testing",
      "Automatic RCD testing sequences",
      "Internal memory for test results"
    ]
  },
  {
    id: "multi-meters",
    name: "Multi Functional Meters",
    slug: "multi-functional-meters",
    description: "Measure multiple electrical parameters",
    icon: BarChart2,
    detailPath: "/measure/multi-functional-meters",
    features: [
      "Combined power quality and safety testing",
      "Data logging and trend analysis",
      "Wireless connectivity for remote monitoring"
    ]
  },
];

// Define the product type
interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  icon: React.ElementType;
  detailPath: string;
  features: string[];
}

const Measure = () => {
  // Create groups of products for each section
  const firstRow = measureProducts.slice(0, 3);
  const secondRow = measureProducts.slice(3, 6);
  const thirdRow = measureProducts.slice(6, 9);
  const fourthRow = measureProducts.slice(9);

  // Modern product card component
  const ProductCard = ({ product }: { product: Product }) => {
    const Icon = product.icon;
    return (
      <Card className="h-full overflow-hidden bg-white shadow-sm hover:shadow-md transition-all duration-300 border-0 hover:border-yellow-200 group">
        <CardHeader className="pb-0">
          <div className="flex items-center mb-4">
            <div className="p-3 bg-yellow-100 rounded-full mr-4 group-hover:bg-yellow-200 transition-colors">
              <Icon className="h-6 w-6 text-yellow-600" />
            </div>
            <CardTitle className="text-xl font-bold text-yellow-900">{product.name}</CardTitle>
          </div>
          <CardDescription className="text-yellow-700 mt-2">{product.description}</CardDescription>

          {product.features && (
            <div className="mt-4">
              <ul className="space-y-2">
                {product.features.map((feature: string, idx: number) => (
                  <li key={idx} className="flex items-start">
                    <div className="mr-2 mt-1 text-yellow-500">
                      <div className="h-1.5 w-1.5 rounded-full bg-yellow-500"></div>
                    </div>
                    <span className="text-sm text-yellow-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </CardHeader>

        <CardFooter className="mt-auto pt-4">
          <Link to={product.detailPath} className="w-full">
            <Button
              className="w-full bg-yellow-500 hover:bg-yellow-600 text-white rounded-md flex items-center justify-center group"
            >
              <span>Learn More</span>
              <ChevronRight size={16} className="ml-1 transition-transform duration-300 group-hover:translate-x-1" />
            </Button>
          </Link>
        </CardFooter>
      </Card>
    );
  };

  return (
    <NewLayout>
      {/* Container to limit page width */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-8">
        {/* Hero Section with original measurement data but modern style */}
        <div className="py-16 mb-16">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-8 items-center">
            <div className="md:col-span-7">
              <h1 className="text-3xl md:text-5xl font-bold text-yellow-500 mb-4 leading-tight">
                TEST AND MEASUREMENT INSTRUMENTS
              </h1>
              <div className="w-24 h-1 bg-yellow-400 mb-6"></div>
              <p className="text-yellow-800 text-lg max-w-2xl">
                High-quality instruments designed for electrical professionals who demand accuracy and reliability in every measurement
              </p>
              <div className="flex flex-wrap gap-4 mt-8">
                <Link to="/contact/sales">
                  <Button className="bg-yellow-500 hover:bg-yellow-600 text-white rounded-full px-6 py-2 font-medium flex items-center gap-2">
                    <span>Enquiry</span>
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </div>

            <div className="md:col-span-5">
              <div className="relative w-72 h-72 mx-auto">
                {/* Glowing background effect */}
                <div className="absolute inset-0 rounded-full bg-yellow-200/30 blur-xl"></div>

                {/* Main center icon with pulsing animation */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 p-5 rounded-full bg-yellow-300/40 backdrop-blur-sm animate-pulse">
                  <Gauge className="h-20 w-20 text-yellow-800 drop-shadow-lg" />
                </div>

                {/* Orbiting icons */}
                <div className="absolute inset-0">
                  {/* Top icon */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-yellow-200/40 backdrop-blur-sm rounded-full p-3 shadow-lg">
                    <Ruler className="h-10 w-10 text-yellow-700" />
                  </div>

                  {/* Right icon */}
                  <div className="absolute top-1/2 right-0 transform translate-x-1/2 -translate-y-1/2 bg-yellow-200/40 backdrop-blur-sm rounded-full p-3 shadow-lg">
                    <Compass className="h-10 w-10 text-yellow-700" />
                  </div>

                  {/* Bottom icon */}
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 bg-yellow-200/40 backdrop-blur-sm rounded-full p-3 shadow-lg">
                    <Maximize className="h-10 w-10 text-yellow-700" />
                  </div>

                  {/* Left icon */}
                  <div className="absolute top-1/2 left-0 transform -translate-x-1/2 -translate-y-1/2 bg-yellow-200/40 backdrop-blur-sm rounded-full p-3 shadow-lg">
                    <Minimize className="h-10 w-10 text-yellow-700" />
                  </div>
                </div>

                {/* Floating small icons with different animation */}
                <div className="absolute top-1/4 right-1/4 bg-yellow-200/40 backdrop-blur-sm rounded-full p-2 animate-float">
                  <Clock className="h-6 w-6 text-yellow-700" />
                </div>

                <div className="absolute bottom-1/4 left-1/4 bg-yellow-200/40 backdrop-blur-sm rounded-full p-2 animate-float" style={{ animationDelay: '2s' }}>
                  <Cpu className="h-6 w-6 text-yellow-700" />
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* First Row of Products */}
      <div className="mb-16">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold text-yellow-800">Power Analysis Tools</h2>
          <div className="h-px bg-yellow-200 flex-grow ml-6"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {firstRow.map((product, index) => (
            <motion.div
              key={product.slug}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.1 }}
            >
              <ProductCard product={product} />
            </motion.div>
          ))}
        </div>
      </div>

      {/* Second Row of Products */}
      <div className="mb-16">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold text-yellow-800">Testing Instruments</h2>
          <div className="h-px bg-yellow-200 flex-grow ml-6"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {secondRow.map((product, index) => (
            <motion.div
              key={product.slug}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.1 }}
            >
              <ProductCard product={product} />
            </motion.div>
          ))}
        </div>
      </div>

      {/* Third Row of Products */}
      <div className="mb-16">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold text-yellow-800">Measurement Meters</h2>
          <div className="h-px bg-yellow-200 flex-grow ml-6"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {thirdRow.map((product, index) => (
            <motion.div
              key={product.slug}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.1 }}
            >
              <ProductCard product={product} />
            </motion.div>
          ))}
        </div>
      </div>

      {/* Fourth Row of Products */}
      {fourthRow.length > 0 && (
        <div className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-yellow-800">Specialized Instruments</h2>
            <div className="h-px bg-yellow-200 flex-grow ml-6"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {fourthRow.map((product, index) => (
              <motion.div
                key={product.slug}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <ProductCard product={product} />
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Feature Section */}
      <div className="mb-16 bg-yellow-50 rounded-lg p-8 shadow-sm">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-yellow-800 mb-3">KRYKARD Measurement Benefits</h2>
          <p className="text-yellow-700 max-w-2xl mx-auto">Our flagship products deliver measurable results for industrial and commercial applications</p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
          {[
            { title: "Precision Engineering", value: "±0.1% accuracy", icon: Ruler },
            { title: "Durable Design", value: "IP67 rated protection", icon: Shield },
            { title: "Data Connectivity", value: "Wireless integration", icon: Activity },
            { title: "Expert Support", value: "Lifetime technical help", icon: Cpu }
          ].map((feature, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-sm text-center hover:shadow-md transition-all duration-300">
              <div className="flex justify-center mb-3">
                {React.createElement(feature.icon, { className: "h-8 w-8 text-yellow-500" })}
              </div>
              <h3 className="text-yellow-800 font-bold mb-2">{feature.title}</h3>
              <p className="text-yellow-600">{feature.value}</p>
            </div>
          ))}
        </div>
      </div>

      <div className="mb-16">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-yellow-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-yellow-100 rounded-full mr-4">
                <Gauge className="h-6 w-6 text-yellow-600" />
              </div>
              <h3 className="text-xl font-bold text-yellow-900">Precision Measurement</h3>
            </div>
            <p className="text-yellow-700">Our instruments provide highly accurate measurements with minimal error margins, ensuring reliable data for critical applications.</p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-yellow-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-yellow-100 rounded-full mr-4">
                <Shield className="h-6 w-6 text-yellow-600" />
              </div>
              <h3 className="text-xl font-bold text-yellow-900">Rugged Reliability</h3>
            </div>
            <p className="text-yellow-700">Built to withstand harsh industrial environments with robust construction and protective features for long-term durability.</p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-yellow-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-yellow-100 rounded-full mr-4">
                <Activity className="h-6 w-6 text-yellow-600" />
              </div>
              <h3 className="text-xl font-bold text-yellow-900">Advanced Analytics</h3>
            </div>
            <p className="text-yellow-700">Sophisticated data processing capabilities that transform raw measurements into actionable insights for better decision-making.</p>
          </div>
        </div>
      </div>

      {/* Contact Information Section */}
      <div className="mt-16 mb-16 bg-yellow-50 p-10 rounded-lg shadow-sm overflow-hidden relative">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-yellow-600 mb-6">Need Expert Advice?</h2>
          <p className="text-yellow-700 text-lg mb-8 max-w-2xl mx-auto">
            Our team of experts is ready to help you with product specifications, custom solutions,
            pricing, and any other details you need about KRYKARD Measurement products.
          </p>
          <div className="flex justify-center">
            <Link to="/contact/sales">
              <Button className="bg-yellow-500 hover:bg-yellow-600 text-white shadow-md px-8 py-2 text-lg font-medium">
                Contact Our Experts
              </Button>
            </Link>
          </div>
        </div>
      </div>

      </div>
    </NewLayout>
  );
};

export default Measure;