// Script to convert pages to use EnhancedLayout with hero sections
import fs from 'fs';
import path from 'path';

// Pages to convert with their hero information
const pagesToConvert = [
  {
    file: 'src/pages/Measure.tsx',
    title: 'MEASURE',
    subtitle: 'Precision instruments for electrical measurements',
    category: 'measure'
  },
  {
    file: 'src/pages/Protect.tsx', 
    title: 'PROTECT',
    subtitle: 'Safeguard your electrical systems',
    category: 'protect'
  },
  {
    file: 'src/pages/Conserve.tsx',
    title: 'CONSERVE', 
    subtitle: 'Optimize energy consumption',
    category: 'conserve'
  },
  {
    file: 'src/pages/measure/InstallationTesters.tsx',
    title: 'INSTALLATION TESTERS',
    subtitle: 'Professional electrical test and measurement equipment',
    category: 'measure'
  },
  {
    file: 'src/pages/measure/InsulationTesters.tsx',
    title: 'Insulation Testers',
    subtitle: 'Advanced insulation testing solutions',
    category: 'measure'
  },
  {
    file: 'src/pages/protect/StaticStabilizers.tsx',
    title: 'Static Voltage Stabilizers',
    subtitle: 'Reliable voltage regulation solutions',
    category: 'protect'
  },
  {
    file: 'src/pages/protect/ServoStabilizers.tsx',
    title: 'Servo Voltage Stabilizers', 
    subtitle: 'Precision voltage control systems',
    category: 'protect'
  },
  {
    file: 'src/pages/conserve/OnPremiseSystems.tsx',
    title: 'On-Premise Energy Management',
    subtitle: 'Complete energy monitoring and control solutions',
    category: 'conserve'
  },
  {
    file: 'src/pages/conserve/EnergyAudits.tsx',
    title: 'Energy Audits',
    subtitle: 'Comprehensive energy efficiency assessments',
    category: 'conserve'
  }
];

function convertPage(pageInfo) {
  try {
    if (!fs.existsSync(pageInfo.file)) {
      console.log(`❌ File not found: ${pageInfo.file}`);
      return false;
    }

    let content = fs.readFileSync(pageInfo.file, 'utf8');
    let modified = false;

    // Replace NewLayout import with EnhancedLayout import
    if (content.includes('import NewLayout from "@/components/NewLayout";')) {
      content = content.replace(
        'import NewLayout from "@/components/NewLayout";',
        'import EnhancedLayout from "@/components/EnhancedLayout";'
      );
      modified = true;
    }

    // Replace NewLayout usage with EnhancedLayout (opening tag)
    const newLayoutRegex = /<NewLayout>/g;
    if (content.match(newLayoutRegex)) {
      const enhancedLayoutTag = `<EnhancedLayout
        title="${pageInfo.title}"
        subtitle="${pageInfo.subtitle}"
        category="${pageInfo.category}"
      >`;
      content = content.replace(newLayoutRegex, enhancedLayoutTag);
      modified = true;
    }

    // Replace closing NewLayout tag
    if (content.includes('</NewLayout>')) {
      content = content.replace(/\s*<\/NewLayout>/g, '</EnhancedLayout>');
      modified = true;
    }

    if (modified) {
      fs.writeFileSync(pageInfo.file, content, 'utf8');
      console.log(`✅ Converted: ${pageInfo.file}`);
      console.log(`   Title: ${pageInfo.title}`);
      console.log(`   Category: ${pageInfo.category}`);
      return true;
    } else {
      console.log(`⏭️  No changes needed: ${pageInfo.file}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${pageInfo.file}:`, error.message);
    return false;
  }
}

// Main execution
console.log('🚀 Starting NewLayout to EnhancedLayout conversion...\n');

let totalConverted = 0;
for (const pageInfo of pagesToConvert) {
  if (convertPage(pageInfo)) {
    totalConverted++;
  }
  console.log(''); // Add spacing between conversions
}

console.log(`✨ Conversion complete! ${totalConverted} files converted.`);
