import { useRef } from "react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import EnhancedLayout from "@/components/EnhancedLayout";
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { ChevronRight, Users, Leaf, Briefcase, Building, FileText, ArrowRight, Shield, Zap, Globe, Calendar } from "lucide-react";

const aboutSections = [
  {
    name: "Company",
    slug: "company",
    description: "Learn about our history and mission",
    image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?q=80&w=2070&auto=format&fit=crop",
    icon: Building,
    color: "blue"
  },
  {
    name: "Certificates",
    slug: "certificates",
    description: "Our quality certifications and compliance standards",
    image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?q=80&w=2070&auto=format&fit=crop",
    icon: Shield,
    color: "teal"
  },
  {
    name: "Sustainability",
    slug: "sustainability",
    description: "Our commitment to environmental responsibility",
    image: "https://images.unsplash.com/photo-1473448912268-2022ce9509d8?q=80&w=2041&auto=format&fit=crop",
    icon: Leaf,
    color: "green"
  },
  {
    name: "Company Events",
    slug: "events",
    description: "Conferences, trade shows, and community engagements",
    image: "https://images.unsplash.com/photo-1540575467063-178a50c2df87?q=80&w=2070&auto=format&fit=crop",
    icon: Calendar,
    color: "yellow"
  },
  {
    name: "Vision & Mission",
    slug: "vision-mission",
    description: "Our guiding vision and mission for a sustainable future",
    image: "https://images.unsplash.com/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop",
    icon: Briefcase,
    color: "blue"
  },
  {
    name: "Infrastructure & Network",
    slug: "infrastructure-network",
    description: "Our robust infrastructure and global network",
    image: "https://images.unsplash.com/photo-1464983953574-0892a716854b?q=80&w=2070&auto=format&fit=crop",
    icon: Globe,
    color: "green"
  }
];

const coreValues = [
  {
    icon: "💡",
    name: "Innovation",
    description: "Constantly pushing boundaries to create cutting-edge energy solutions that transform how businesses manage power.",
    bgColor: "bg-blue-50",
    textColor: "text-blue-600"
  },
  {
    icon: "🌱",
    name: "Sustainability",
    description: "Committed to environmentally responsible practices in all operations and developing solutions that reduce environmental impact.",
    bgColor: "bg-green-50",
    textColor: "text-green-600"
  },
  {
    icon: "🏆",
    name: "Excellence",
    description: "Delivering the highest quality products and services to exceed expectations and set new industry standards.",
    bgColor: "bg-yellow-50",
    textColor: "text-yellow-600"
  },
  {
    icon: "🤝",
    name: "Integrity",
    description: "Operating with honesty, transparency, and ethical business practices in all our relationships and transactions.",
    bgColor: "bg-blue-50",
    textColor: "text-blue-600"
  },
  {
    icon: "👥",
    name: "Customer Focus",
    description: "Putting our clients' needs at the center of everything we do, ensuring solutions that address real-world challenges.",
    bgColor: "bg-green-50",
    textColor: "text-green-600"
  },
  {
    icon: "🔄",
    name: "Collaboration",
    description: "Working together with partners and clients to achieve shared goals and create innovative energy solutions.",
    bgColor: "bg-yellow-50",
    textColor: "text-yellow-600"
  }
];

const About = () => {
  // References for scroll animations
  const heroRef = useRef(null);

  return (
    <EnhancedLayout
      title="About KRYKARD"
      subtitle="Pioneering energy solutions for a sustainable future"
      category="about"
    >
      {/* Hero Section - Enhanced with modern design */}
      <div className="w-full py-16 mb-16 relative overflow-hidden" ref={heroRef}>
        {/* Subtle background elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/5 via-green-50/5 to-yellow-50/5"></div>
        <motion.div
          className="absolute -top-24 -right-24 w-96 h-96 rounded-full opacity-10 bg-blue-500/20 blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            y: [0, 30, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute -bottom-24 -left-24 w-64 h-64 rounded-full opacity-10 bg-green-500/20 blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            y: [0, -40, 0],
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <div className="max-w-6xl mx-auto px-4 sm:px-6 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-8 items-center">
            <div className="md:col-span-7">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <span className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-blue-500/10 text-blue-600 border border-blue-500/20 mb-4">
                  <span className="flex h-2 w-2 rounded-full bg-blue-400 mr-2"></span>
                  About Us
                </span>
              </motion.div>

              <motion.h1
                className="text-2xl md:text-4xl font-bold mb-4 leading-tight tracking-tight"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
              >
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800">
                  POWERING THE FUTURE
                </span>{' '}
                <span className="text-gray-900">THROUGH INNOVATION</span>
              </motion.h1>

              <motion.p
                className="text-gray-800 text-lg max-w-2xl leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                At KRYKARD, we are dedicated to providing innovative energy management solutions that help organizations measure, protect, and conserve their power infrastructure. Our mission is to enable sustainable energy practices that benefit both our clients and the environment.
              </motion.p>

              <div className="mt-8 space-y-4">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: 0.3 }}
                  className="flex items-center"
                >
                  <div className="p-2 rounded-lg bg-blue-100 mr-4">
                    <Building className="h-5 w-5 text-blue-700" />
                  </div>
                  <span className="text-gray-800 font-medium">Founded in 2011 with headquarters in Chennai, India</span>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: 0.4 }}
                  className="flex items-center"
                >
                  <div className="p-2 rounded-lg bg-green-100 mr-4">
                    <Users className="h-5 w-5 text-green-700" />
                  </div>
                  <span className="text-gray-800 font-medium">Team of 200+ energy management professionals</span>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: 0.5 }}
                  className="flex items-center"
                >
                  <div className="p-2 rounded-lg bg-yellow-100 mr-4">
                    <Globe className="h-5 w-5 text-yellow-700" />
                  </div>
                  <span className="text-gray-800 font-medium">Serving clients in 25+ countries worldwide</span>
                </motion.div>
              </div>

              <motion.div
                className="flex flex-wrap gap-4 mt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                <Link to="/about/company">
                  <Button className="bg-gradient-to-r from-blue-600 to-blue-800 text-white hover:from-blue-700 hover:to-blue-900 shadow-md font-medium flex items-center group">
                    Our Story <ArrowRight className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                  </Button>
                </Link>
                <Link to="/contact/sales">
                  <Button variant="outline" className="text-blue-700 border-blue-300 hover:bg-blue-50 font-medium group shadow-sm">
                    Contact Us <ArrowRight className="ml-2 h-5 w-5 opacity-0 transition-all duration-300 group-hover:opacity-100 group-hover:translate-x-1" />
                  </Button>
                </Link>
              </motion.div>
            </div>
            <div className="md:col-span-5 hidden md:block">
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8 }}
                className="relative"
              >
                {/* Removed background container for better UI blending */}
                <div className="relative">
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 via-green-500/20 to-yellow-500/20 rounded-xl blur-xl opacity-70"></div>

                  {/* Content */}
                  <div className="relative backdrop-blur-sm rounded-xl overflow-hidden border border-white/20 shadow-xl p-8">
                    <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-blue-800 mb-6">Our Impact</h3>

                    <div className="space-y-6">
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                        className="flex items-start"
                      >
                        <div className="p-3 rounded-lg bg-blue-100/80 mr-4 shadow-sm">
                          <Zap className="h-6 w-6 text-blue-700" />
                        </div>
                        <div>
                          <h4 className="text-lg font-semibold text-blue-700 mb-1">Energy Saved</h4>
                          <p className="text-gray-800">Our solutions have helped clients save over 1.2 million kWh annually</p>
                        </div>
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: 0.3 }}
                        className="flex items-start"
                      >
                        <div className="p-3 rounded-lg bg-green-100/80 mr-4 shadow-sm">
                          <Leaf className="h-6 w-6 text-green-700" />
                        </div>
                        <div>
                          <h4 className="text-lg font-semibold text-green-700 mb-1">Environmental Impact</h4>
                          <p className="text-gray-800">Reduced carbon emissions by 15,000+ metric tons</p>
                        </div>
                      </motion.div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* What We Do Section - Enhanced with modern design */}
      <div className="mb-16 relative overflow-hidden">
        {/* Subtle background elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/5 via-green-50/5 to-yellow-50/5"></div>
        <motion.div
          className="absolute top-1/4 right-1/4 w-96 h-96 rounded-full opacity-5 bg-blue-500/20 blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            x: [0, 30, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-1/4 left-1/4 w-64 h-64 rounded-full opacity-5 bg-green-500/20 blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            x: [0, -40, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <div className="relative z-10 max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.6 }}
              className="relative"
            >
              {/* Modern badge */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="mb-6"
              >
                <span className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-blue-500/10 text-blue-600 border border-blue-500/20">
                  <span className="flex h-2 w-2 rounded-full bg-blue-400 mr-2"></span>
                  Our Expertise
                </span>
              </motion.div>

              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800">What We Do</span>
              </h2>

              <p className="text-gray-800 max-w-3xl mx-auto text-lg">
                KRYKARD provides cutting-edge energy management solutions to help businesses optimize power usage, reduce costs, and embrace sustainability
              </p>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ delay: 0.1, duration: 0.5 }}
              className="group h-full"
              whileHover={{ y: -8, transition: { duration: 0.3 } }}
            >
              <div className="relative h-full rounded-xl overflow-hidden">
                {/* Subtle card glow effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/40 to-blue-700/40 rounded-xl blur-lg opacity-20 group-hover:opacity-30 transition-all duration-500 -z-10"></div>

                {/* Main card content */}
                <div className="relative h-full flex flex-col bg-gradient-to-br from-blue-900/10 to-blue-950/10 border border-white/10 rounded-xl p-8 overflow-hidden transition-all duration-500 group-hover:border-blue-500/20 shadow-lg">
                  {/* Animated accent line at top */}
                  <div className="absolute top-0 left-0 right-0 h-1 bg-blue-400/60 transform scale-x-0 group-hover:scale-x-100 origin-left transition-transform duration-500 ease-out"></div>

                  {/* Title and Icon */}
                  <div className="flex items-center mb-6 relative">
                    <div className="p-3 rounded-lg bg-blue-600 text-white shadow-lg transition-all duration-500 group-hover:scale-110">
                      <Shield className="h-8 w-8" />
                    </div>
                    <h3 className="text-2xl font-bold text-blue-700 ml-4">Power Protection</h3>
                  </div>

                  {/* Description */}
                  <p className="text-gray-800 mb-6 flex-grow relative text-lg leading-relaxed">
                    Our Static Voltage Regulators provide comprehensive protection against voltage fluctuations, ensuring uninterrupted power supply for critical operations.
                  </p>

                  {/* CTA button */}
                  <div className="mt-auto">
                    <Link to="/protect" className="inline-flex items-center gap-2 px-4 py-2 rounded-lg border border-blue-600/40 hover:bg-blue-600/10 text-blue-700 transition-all duration-300">
                      <span>Learn More</span>
                      <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                    </Link>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className="group h-full"
              whileHover={{ y: -8, transition: { duration: 0.3 } }}
            >
              <div className="relative h-full rounded-xl overflow-hidden">
                {/* Subtle card glow effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-green-500/40 to-green-700/40 rounded-xl blur-lg opacity-20 group-hover:opacity-30 transition-all duration-500 -z-10"></div>

                {/* Main card content */}
                <div className="relative h-full flex flex-col bg-gradient-to-br from-green-900/10 to-green-950/10 border border-white/10 rounded-xl p-8 overflow-hidden transition-all duration-500 group-hover:border-green-500/20 shadow-lg">
                  {/* Animated accent line at top */}
                  <div className="absolute top-0 left-0 right-0 h-1 bg-green-400/60 transform scale-x-0 group-hover:scale-x-100 origin-left transition-transform duration-500 ease-out"></div>

                  {/* Title and Icon */}
                  <div className="flex items-center mb-6 relative">
                    <div className="p-3 rounded-lg bg-green-600 text-white shadow-lg transition-all duration-500 group-hover:scale-110">
                      <Leaf className="h-8 w-8" />
                    </div>
                    <h3 className="text-2xl font-bold text-green-700 ml-4">Energy Efficiency</h3>
                  </div>

                  {/* Description */}
                  <p className="text-gray-800 mb-6 flex-grow relative text-lg leading-relaxed">
                    We develop intelligent systems that optimize energy consumption, helping organizations reduce their carbon footprint while cutting operational costs.
                  </p>

                  {/* CTA button */}
                  <div className="mt-auto">
                    <Link to="/conserve" className="inline-flex items-center gap-2 px-4 py-2 rounded-lg border border-green-600/40 hover:bg-green-600/10 text-green-700 transition-all duration-300">
                      <span>Learn More</span>
                      <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                    </Link>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="group h-full"
              whileHover={{ y: -8, transition: { duration: 0.3 } }}
            >
              <div className="relative h-full rounded-xl overflow-hidden">
                {/* Subtle card glow effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/40 to-yellow-700/40 rounded-xl blur-lg opacity-20 group-hover:opacity-30 transition-all duration-500 -z-10"></div>

                {/* Main card content */}
                <div className="relative h-full flex flex-col bg-gradient-to-br from-yellow-900/10 to-yellow-950/10 border border-white/10 rounded-xl p-8 overflow-hidden transition-all duration-500 group-hover:border-yellow-500/20 shadow-lg">
                  {/* Animated accent line at top */}
                  <div className="absolute top-0 left-0 right-0 h-1 bg-yellow-400/60 transform scale-x-0 group-hover:scale-x-100 origin-left transition-transform duration-500 ease-out"></div>

                  {/* Title and Icon */}
                  <div className="flex items-center mb-6 relative">
                    <div className="p-3 rounded-lg bg-yellow-600 text-white shadow-lg transition-all duration-500 group-hover:scale-110">
                      <Zap className="h-8 w-8" />
                    </div>
                    <h3 className="text-2xl font-bold text-yellow-700 ml-4">Innovation Research</h3>
                  </div>

                  {/* Description */}
                  <p className="text-gray-800 mb-6 flex-grow relative text-lg leading-relaxed">
                    Our dedicated R&D team continuously works on developing next-generation energy management technologies that address emerging industry challenges.
                  </p>

                  {/* CTA button */}
                  <div className="mt-auto">
                    <Link to="/about/company" className="inline-flex items-center gap-2 px-4 py-2 rounded-lg border border-yellow-600/40 hover:bg-yellow-600/10 text-yellow-700 transition-all duration-300">
                      <span>Learn More</span>
                      <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                    </Link>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* About Sections Cards - Enhanced with modern design */}
      <div className="mb-16 relative overflow-hidden">
        {/* Subtle background elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/5 via-green-50/5 to-yellow-50/5"></div>
        <motion.div
          className="absolute bottom-1/3 right-1/3 w-96 h-96 rounded-full opacity-5 bg-blue-500/20 blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            y: [0, 30, 0],
          }}
          transition={{
            duration: 22,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <div className="relative z-10 max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.6 }}
              className="relative"
            >
              {/* Modern badge */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="mb-6"
              >
                <span className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-blue-500/10 text-blue-600 border border-blue-500/20">
                  <span className="flex h-2 w-2 rounded-full bg-blue-400 mr-2"></span>
                  Discover More
                </span>
              </motion.div>

              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800">Explore Our Company</span>
              </h2>

              <p className="text-gray-800 max-w-2xl mx-auto text-lg">
                Discover different aspects of KRYKARD and what makes us unique
              </p>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {aboutSections.map((section, index) => {
              const Icon = section.icon;
              const colorMap = {
                blue: {
                  gradientFrom: "from-blue-500/40",
                  gradientTo: "to-blue-700/40",
                  bgFrom: "from-blue-900/10",
                  bgTo: "to-blue-950/10",
                  accent: "bg-blue-400/60",
                  iconBg: "bg-blue-600",
                  text: "text-blue-700",
                  border: "border-blue-500/20",
                  buttonBorder: "border-blue-600/40",
                  buttonHover: "hover:bg-blue-600/10"
                },
                green: {
                  gradientFrom: "from-green-500/40",
                  gradientTo: "to-green-700/40",
                  bgFrom: "from-green-900/10",
                  bgTo: "to-green-950/10",
                  accent: "bg-green-400/60",
                  iconBg: "bg-green-600",
                  text: "text-green-700",
                  border: "border-green-500/20",
                  buttonBorder: "border-green-600/40",
                  buttonHover: "hover:bg-green-600/10"
                },
                yellow: {
                  gradientFrom: "from-yellow-500/40",
                  gradientTo: "to-yellow-700/40",
                  bgFrom: "from-yellow-900/10",
                  bgTo: "to-yellow-950/10",
                  accent: "bg-yellow-400/60",
                  iconBg: "bg-yellow-600",
                  text: "text-yellow-700",
                  border: "border-yellow-500/20",
                  buttonBorder: "border-yellow-600/40",
                  buttonHover: "hover:bg-yellow-600/10"
                }
              };

              const colors = colorMap[section.color] || colorMap.blue;

              return (
                <motion.div
                  key={section.slug}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, amount: 0.3 }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                  className="group h-full"
                  whileHover={{ y: -8, transition: { duration: 0.3 } }}
                >
                  <div className="relative h-full rounded-xl overflow-hidden">
                    {/* Subtle card glow effect */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${colors.gradientFrom} ${colors.gradientTo} rounded-xl blur-lg opacity-20 group-hover:opacity-30 transition-all duration-500 -z-10`}></div>

                    {/* Main card content */}
                    <div className={`relative h-full flex flex-col bg-gradient-to-br ${colors.bgFrom} ${colors.bgTo} border border-white/10 rounded-xl overflow-hidden transition-all duration-500 group-hover:${colors.border} shadow-lg`}>
                      {/* Animated accent line at top */}
                      <div className={`absolute top-0 left-0 right-0 h-1 ${colors.accent} transform scale-x-0 group-hover:scale-x-100 origin-left transition-transform duration-500 ease-out`}></div>

                      {/* Image section */}
                      <div className="h-48 overflow-hidden relative">
                        <img
                          src={section.image}
                          alt={section.name}
                          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-60 group-hover:opacity-40 transition-opacity duration-300"></div>

                        {/* Icon */}
                        <div className={`absolute top-4 right-4 ${colors.iconBg} rounded-full p-2 transition-all duration-300 shadow-lg group-hover:scale-110`}>
                          <Icon className="h-6 w-6 text-white" />
                        </div>
                      </div>

                      {/* Content section */}
                      <div className="p-6">
                        <h3 className={`text-xl font-bold ${colors.text} mb-2`}>{section.name}</h3>
                        <p className="text-gray-800 font-medium mb-6">{section.description}</p>

                        {/* CTA button */}
                        <div className="mt-auto">
                          <Link to={`/about/${section.slug}`} className="w-full">
                            <button className={`w-full inline-flex items-center justify-center gap-2 px-4 py-2 rounded-lg border ${colors.buttonBorder} ${colors.buttonHover} ${colors.text} transition-all duration-300 group`}>
                              <span>Learn More</span>
                              <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                            </button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Company Values Section - Enhanced with modern design */}
      <div className="mb-16 relative overflow-hidden">
        {/* Subtle background elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/10 via-white/5 to-blue-50/10 rounded-xl"></div>
        <motion.div
          className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full opacity-5 bg-blue-500/20 blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            x: [0, -30, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <div className="relative z-10 max-w-6xl mx-auto px-4 py-16">
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.6 }}
              className="relative"
            >
              {/* Modern badge */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="mb-6"
              >
                <span className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-blue-500/10 text-blue-600 border border-blue-500/20">
                  <span className="flex h-2 w-2 rounded-full bg-blue-400 mr-2"></span>
                  Our Principles
                </span>
              </motion.div>

              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800">Our Core Values</span>
              </h2>

              <p className="text-gray-800 max-w-2xl mx-auto text-lg">
                These principles guide everything we do, from product development to customer service
              </p>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {coreValues.map((value, index) => {
              // Define modern color mappings
              const colorMappings = {
                "bg-blue-50": {
                  glow: "from-blue-500/40 to-blue-700/40",
                  bg: "from-blue-900/5 to-blue-950/5",
                  accent: "bg-blue-400/60",
                  iconBg: "bg-gradient-to-br from-blue-500 to-blue-700",
                  text: "text-blue-700"
                },
                "bg-green-50": {
                  glow: "from-green-500/40 to-green-700/40",
                  bg: "from-green-900/5 to-green-950/5",
                  accent: "bg-green-400/60",
                  iconBg: "bg-gradient-to-br from-green-500 to-green-700",
                  text: "text-green-700"
                },
                "bg-yellow-50": {
                  glow: "from-yellow-500/40 to-yellow-700/40",
                  bg: "from-yellow-900/5 to-yellow-950/5",
                  accent: "bg-yellow-400/60",
                  iconBg: "bg-gradient-to-br from-yellow-500 to-yellow-700",
                  text: "text-yellow-700"
                }
              };

              const colors = colorMappings[value.bgColor] || colorMappings["bg-blue-50"];

              return (
                <motion.div
                  key={value.name}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, amount: 0.3 }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                  className="group h-full"
                  whileHover={{ y: -8, transition: { duration: 0.3 } }}
                >
                  <div className="relative h-full rounded-xl overflow-hidden">
                    {/* Subtle card glow effect */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${colors.glow} rounded-xl blur-lg opacity-10 group-hover:opacity-20 transition-all duration-500 -z-10`}></div>

                    {/* Main card content */}
                    <div className={`relative h-full flex flex-col bg-gradient-to-br ${colors.bg} border border-white/20 rounded-xl p-8 overflow-hidden transition-all duration-500 group-hover:border-blue-500/20 shadow-lg`}>
                      {/* Animated accent line at top */}
                      <div className={`absolute top-0 left-0 right-0 h-1 ${colors.accent} transform scale-x-0 group-hover:scale-x-100 origin-left transition-transform duration-500 ease-out`}></div>

                      {/* Icon and Title */}
                      <div className="flex items-center mb-6">
                        <div className={`p-4 ${colors.iconBg} rounded-xl mr-4 shadow-lg transition-all duration-300 group-hover:scale-110 text-white`}>
                          <span className="text-2xl" aria-hidden="true">{value.icon}</span>
                        </div>
                        <h3 className={`text-xl font-bold ${colors.text}`}>{value.name}</h3>
                      </div>

                      {/* Description */}
                      <p className="text-gray-800 font-medium leading-relaxed">{value.description}</p>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Contact Information Section - Enhanced with modern design */}
      <div className="mt-16 mb-16 relative overflow-hidden">
        {/* Subtle background elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-blue-700/5 to-blue-900/5 rounded-xl"></div>
        <motion.div
          className="absolute -bottom-24 -right-24 w-96 h-96 rounded-full opacity-10 bg-blue-500/20 blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            y: [0, -20, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6 }}
          className="relative z-10 py-16 px-8 rounded-xl overflow-hidden"
        >
          <div className="max-w-4xl mx-auto text-center">
            {/* Modern badge */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="mb-6 flex justify-center"
            >
              <span className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-blue-500/10 text-blue-600 border border-blue-500/20">
                <span className="flex h-2 w-2 rounded-full bg-blue-400 mr-2"></span>
                Get Started
              </span>
            </motion.div>

            <motion.h2
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-3xl md:text-4xl font-bold mb-6"
            >
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800">Ready to Learn More?</span>
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-gray-800 text-lg mb-8 max-w-2xl mx-auto leading-relaxed"
            >
              Contact our team to discover how KRYKARD can help your organization optimize energy usage and reduce costs
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="flex justify-center"
            >
              <Link to="/contact/sales">
                <button className="bg-gradient-to-r from-blue-600 to-blue-800 hover:from-blue-700 hover:to-blue-900 text-white shadow-lg px-8 py-3 rounded-lg text-lg font-semibold group flex items-center transition-all duration-300 hover:shadow-blue-500/20 hover:shadow-xl">
                  Get in Touch <ArrowRight className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                </button>
              </Link>
            </motion.div>
          </div>

          {/* Decorative elements */}
          <div className="absolute top-0 left-0 w-24 h-24 border-t-2 border-l-2 border-blue-500/20 rounded-tl-xl -mt-3 -ml-3 z-0"></div>
          <div className="absolute bottom-0 right-0 w-24 h-24 border-b-2 border-r-2 border-blue-500/20 rounded-br-xl -mb-3 -mr-3 z-0"></div>
        </motion.div>
      </div>
    </EnhancedLayout>
  );
};

export default About;