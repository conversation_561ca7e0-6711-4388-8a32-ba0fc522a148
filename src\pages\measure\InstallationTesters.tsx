import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import PageLayout from "@/components/layout/PageLayout";
import {
  ArrowRight,
  Check,
  ChevronRight,
  ArrowDownCircle,
  Zap,
  BarChart,
  Gauge,
  Shield,
  FileText,
  ExternalLink
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// PDF URL for brochure
const PDF_URL = "/T&M April 2025.pdf";

// Modern Background Component
const ModernBackground = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-[-1] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Abstract shapes with improved contrast */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-yellow-200 rounded-bl-full opacity-40"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-yellow-300 rounded-tr-full opacity-30"></div>

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
    </div>
  );
};



// Enhanced Hero Section Component with Brochure Button
const HeroSection = ({ onRequestDemo, onViewBrochure }) => {
  return (
    <div className="relative py-8 md:py-12 overflow-hidden font-['Open_Sans']">
      {/* Hero Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-3/4 h-full bg-yellow-50 rounded-bl-[100px] transform -skew-x-12"></div>
        <div className="absolute bottom-20 left-0 w-64 h-64 bg-yellow-400 rounded-full opacity-10"></div>
      </div>

      <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 md:gap-8 items-center">
          {/* Text Content with improved readability */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-3 sm:space-y-4 order-2 lg:order-1"
          >
            <div className="inline-block bg-yellow-400 py-1 px-2 sm:px-3 rounded-full mb-2">
              <span className="text-xs sm:text-sm font-semibold text-gray-900 font-['Open_Sans']">KRYKARD Precision Instruments</span>
            </div>
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 leading-tight font-['Open_Sans']">
              <span className="block sm:inline">INSTALLATION</span> <span className="text-yellow-400">TESTERS</span>
            </h1>

            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-800 leading-relaxed font-medium text-justify font-['Open_Sans']">
              Professional electrical test and measurement equipment compliant with international standards.
            </p>

            <div className="pt-2 flex flex-col sm:flex-row gap-2 sm:gap-3">
              <Button
                className="w-full sm:w-auto px-3 sm:px-4 py-2 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center justify-center space-x-2 font-['Open_Sans'] text-xs sm:text-sm"
                onClick={onRequestDemo}
              >
                <span>Request Demo</span>
                <ArrowRight className="ml-2 h-3 sm:h-4 w-3 sm:w-4" />
              </Button>
              <Button
                className="w-full sm:w-auto px-3 sm:px-4 py-2 bg-white border-2 border-yellow-400 text-gray-900 font-semibold rounded-lg shadow-sm transition duration-300 hover:bg-gray-50 flex items-center justify-center space-x-2 font-['Open_Sans'] text-xs sm:text-sm"
                onClick={onViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-3 sm:h-4 w-3 sm:w-4" />
              </Button>
            </div>
          </motion.div>

          {/* Product Image with improved animation */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative mt-4 sm:mt-6 lg:mt-0 order-1 lg:order-2"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-40 blur-2xl transform scale-110"></div>
            <motion.div
              animate={{
                y: [0, -15, 0],
              }}
              transition={{
                repeat: Infinity,
                duration: 3,
                ease: "easeInOut"
              }}
              className="relative z-10 flex justify-center"
            >
              <img
                src="/installation testers/hero_image.png"
                alt="Installation Tester"
                className="max-h-[200px] sm:max-h-[250px] md:max-h-[300px] lg:max-h-[400px] xl:max-h-[500px] w-auto object-contain drop-shadow-2xl transform scale-100"
              />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

// Animated Product Card with improved styling
const ProductCard = ({
  title,
  image,
  displayInfo,
  features,
  certifications = [],
  colors = {
    primary: 'yellow-400',
    secondary: 'yellow-50'
  },
  onViewDetailsClick
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="group h-full font-['Open_Sans']"
    >
      <div className="h-full rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 bg-white border border-gray-100 flex flex-col">
        {/* Product Image with Colored Background */}
        <div className={`relative p-1 sm:p-2 md:p-3 flex justify-center items-center bg-${colors.secondary} h-48 sm:h-52 md:h-56 lg:h-60 overflow-hidden group-hover:bg-opacity-80 transition-all duration-700`}>
          <motion.img
            src={image}
            alt={title}
            className="h-40 sm:h-44 md:h-48 lg:h-52 w-auto object-contain z-10 drop-shadow-xl transform scale-110"
            whileHover={{ rotate: [-1, 1, -1], transition: { repeat: Infinity, duration: 2 } }}
          />
          <div className={`absolute top-2 left-2 bg-${colors.primary} text-white text-xs font-bold py-1 px-2 rounded-full font-['Open_Sans']`}>
            {title}
          </div>
        </div>

        {/* Product Content - Flex grow to push button to bottom */}
        <div className="p-2 sm:p-3 md:p-4 flex flex-col flex-grow">
          <h3 className="text-sm sm:text-base md:text-lg font-bold text-gray-900 group-hover:text-yellow-400 transition-colors duration-300 font-['Open_Sans'] mb-2">
            {title}
          </h3>

          {/* Key Features - Fixed height container */}
          <div className="space-y-1 flex-grow">
            {features.slice(0, 3).map((feature, idx) => (
              <div key={idx} className="flex items-start">
                <Check className="h-3 w-3 text-yellow-400 mt-0.5 mr-2 flex-shrink-0" />
                <span className="text-gray-800 text-xs sm:text-sm font-medium text-justify font-['Open_Sans'] leading-tight">{feature}</span>
              </div>
            ))}
          </div>

          {/* Specs Badge */}
          <div className="flex flex-wrap gap-1 pt-2 mb-2">
            <span className="inline-block bg-gray-100 rounded-full px-2 py-1 text-xs font-semibold text-gray-700 font-['Open_Sans']">
              {displayInfo}
            </span>
          </div>

          {/* View Details Button - Always at bottom */}
          <Button
            onClick={onViewDetailsClick}
            className={`w-full py-2 px-3 bg-${colors.primary} hover:bg-yellow-500 text-center font-semibold text-gray-900 rounded-lg transition-all duration-300 transform group-hover:-translate-y-1 flex items-center justify-center font-['Open_Sans'] mt-auto text-xs sm:text-sm`}
          >
            <span>View Details</span>
            <ChevronRight className="ml-1 h-3 w-3" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

// Feature Highlight Component with improved styling
const FeatureHighlight = ({ icon, title, description }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      whileHover={{ y: -4, boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)" }}
      className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 p-3 md:p-4 h-full border-b-4 border-yellow-400 font-['Open_Sans']"
    >
      <div className="flex flex-col h-full">
        <div className="bg-gradient-to-br from-yellow-400 to-yellow-400 w-10 h-10 rounded-lg flex items-center justify-center mb-3 shadow-md">
          {icon}
        </div>
        <h3 className="text-base md:text-lg font-bold text-gray-900 mb-2 font-['Open_Sans']">{title}</h3>
        <p className="text-gray-800 flex-grow text-xs md:text-sm text-justify font-['Open_Sans']">{description}</p>
      </div>
    </motion.div>
  );
};

// Tab Component for Features and Measurements with improved readability
const FeaturesMeasurementsTab = ({ activeProductType, activeTab }) => {
  // Features data based on product type
  const features = {
    ca6117: [
      "Display: Large 5.7\" backlit graphic colour LCD screen, 320 x 240 points",
      "An all-in-one instrument for testing electrical installations, compliant with international standards",
      "Suitable for all neutral systems (TT, TN, IT) and ideal for industries, tertiary and residential",
      "Integrated fuse table for quick reading of the results on the instrument",
      "Li-Ion battery for a longer battery life",
      "Measurement of voltage drop for correct sizing of conductor diameters",
      "Loop measurement with 1 mΩ resolution",
      "Continuity measurements at 12 mA or 200 mA",
      "Memory: 3-level storage, 1000 locations to record",
      "PC interface"
    ],
    ca6133: [
      "Display: Custom 231-segment LCD with blue backlighting",
      "Continuity measurement at 0.2A (200mA)",
      "Android application for Report generation",
      "Earth measurement by stake and loop methods",
      "Insulation testing",
      "RCD testing: current and trip time",
      "Automatic test sequences",
      "Power supply by mains-rechargeable batteries, USB socket or vehicle cigarette lighter",
      "Memory: 30 sites x 99 tests"
    ]
  };

  // Measurements data based on product type
  const measurements = {
    ca6117: [
      { label: "Insulation Test voltage", value: "50/100/250/500/1,000 V DC" },
      { label: "Range", value: "0.01 MΩ to 2 GΩ" },
      { label: "Earth Resistance", value: "Upto 40 Ω (3-Pole) & 399.9 Ω (1P selective earth)" },
      { label: "RCD", value: "AC, A and F-type RCDs, B, B+ & EV-type" },
      { label: "Loop impedance", value: "(Zs (L-PE) and Z (L-N or L-L)) - 1P Live earth measurements" },
      { label: "Voltage", value: "550 VAC/DC" },
      { label: "Current", value: "199.9 A" },
      { label: "Frequency", value: "500 Hz" },
      { label: "Phase rotation", value: "20 to 500 VAC" },
      { label: "Active power", value: "Upto 110 kW (1 phase) & 330 kW (3 phase)" },
      { label: "Harmonics", value: "Upto 50th order" }
    ],
    ca6133: [
      { label: "Insulation Test voltage", value: "250 V / 500 V / 1000 V" },
      { label: "Range", value: "0.01 to 999.9 MΩ" },
      { label: "Earth resistance", value: "Upto 2,000 Ω (3P method)" },
      { label: "RCD test", value: "Type AC & RCDs : 30mA, 100 mA, 500 mA, 650 mA" },
      { label: "Loop impedance", value: "Earth loop (Zs), Fault loop (Zi) measurements" },
      { label: "Voltage", value: "Upto 550 VAC & 800.0 VDC" },
      { label: "Current", value: "Upto 200 A" },
      { label: "Frequency", value: "Upto 999.9 Hz" },
      { label: "Phase rotation", value: "45 to 550 V" }
    ]
  };

  if (activeTab === "features") {
    return (
      <div className="bg-white rounded-xl shadow-lg overflow-hidden font-['Open_Sans']">
        <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-2 md:p-3">
          <h3 className="text-base md:text-lg font-bold text-center text-white font-['Open_Sans']">Salient Features</h3>
        </div>
        <div className="p-3 md:p-4">
          <div className="space-y-1">
            {features[activeProductType].map((feature: string, idx: number) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className="flex items-start p-2 hover:bg-yellow-50 rounded-lg transition-colors duration-300"
              >
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-yellow-100 text-yellow-400 flex items-center justify-center mr-3">
                  <Check className="h-3 w-3" />
                </div>
                <span className="text-gray-900 text-sm md:text-base font-semibold text-justify font-['Open_Sans']">{feature}</span>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  } else {
    return (
      <div className="bg-white rounded-xl shadow-lg overflow-hidden font-['Open_Sans']">
        <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-2 md:p-3">
          <h3 className="text-base md:text-lg font-bold text-center text-white font-['Open_Sans']">Measurements</h3>
        </div>
        <div className="p-3 md:p-4">
          <div className="grid md:grid-cols-2 gap-3 md:gap-4">
            {measurements[activeProductType].map((item: { label: string; value: string }, idx: number) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className="bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow duration-300 border border-yellow-100"
              >
                <h4 className="font-bold text-gray-900 mb-2 border-b border-yellow-200 pb-1 text-sm md:text-base font-['Open_Sans']">{item.label}</h4>
                <div className="text-gray-900 text-xs md:text-sm font-medium text-justify font-['Open_Sans']">{item.value}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  }
};

// Comparison Table Component with improved readability
const ComparisonTable = () => {
  const features = [
    { name: 'Display Type', ca6117: '5.7" Colour LCD', ca6133: '231-segment LCD' },
    { name: 'Insulation Test voltage', ca6117: '50-1000 V DC', ca6133: '250-1000 V' },
    { name: 'Range', ca6117: '0.01 MΩ to 2 GΩ', ca6133: '0.01 to 999.9 MΩ' },
    { name: 'Earth Resistance', ca6117: '40 Ω (3-Pole)', ca6133: '2,000 Ω (3P method)' },
    { name: 'RCD Types', ca6117: 'AC, A, F, B, B+, EV', ca6133: 'AC & Type AC' },
    { name: 'Memory Capacity', ca6117: '1000 locations', ca6133: '30 sites x 99 tests' },
    { name: 'PC Interface', ca6117: 'Yes', ca6133: 'Android app' }
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden mt-4 md:mt-6 font-['Open_Sans']">
      <div className="bg-gradient-to-r from-yellow-400 to-yellow-400 p-2 md:p-3">
        <h3 className="text-base md:text-lg font-bold text-center text-white font-['Open_Sans']">Model Comparison</h3>
      </div>
      <div className="p-2 sm:p-3 md:p-4 overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th className="px-2 sm:px-3 md:px-4 py-2 md:py-3 bg-yellow-50 text-left text-xs sm:text-sm font-bold text-gray-900 uppercase tracking-wider rounded-tl-lg font-['Open_Sans']">Feature</th>
              <th className="px-2 sm:px-3 md:px-4 py-2 md:py-3 bg-yellow-50 text-center text-xs sm:text-sm font-bold text-gray-900 uppercase tracking-wider font-['Open_Sans']">CA 6117</th>
              <th className="px-2 sm:px-3 md:px-4 py-2 md:py-3 bg-yellow-50 text-center text-xs sm:text-sm font-bold text-gray-900 uppercase tracking-wider rounded-tr-lg font-['Open_Sans']">CA 6133</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {features.map((feature, idx) => (
              <motion.tr
                key={idx}
                initial={{ opacity: 0, y: 5 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: idx * 0.05 }}
                viewport={{ once: true }}
                className={idx % 2 === 0 ? 'bg-white hover:bg-yellow-50 transition-colors duration-200' : 'bg-gray-50 hover:bg-yellow-50 transition-colors duration-200'}
              >
                <td className="px-2 sm:px-3 md:px-4 py-2 md:py-3 whitespace-nowrap text-xs sm:text-sm font-bold text-gray-900 font-['Open_Sans']">{feature.name}</td>
                <td className="px-2 sm:px-3 md:px-4 py-2 md:py-3 whitespace-nowrap text-xs sm:text-sm text-gray-900 text-center font-medium font-['Open_Sans']">{feature.ca6117}</td>
                <td className="px-2 sm:px-3 md:px-4 py-2 md:py-3 whitespace-nowrap text-xs sm:text-sm text-gray-900 text-center font-medium font-['Open_Sans']">{feature.ca6133}</td>
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};


// Applications Section Component with improved contrast
const ApplicationsSection = () => {
  const applications = [
    {
      title: "Electrical Installations",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      description: "Verify the safety and compliance of electrical installations in residential, commercial, and industrial buildings."
    },
    {
      title: "Industrial Maintenance",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      description: "Regular testing of electrical systems to ensure safety, prevent downtime, and maintain operational efficiency."
    },
    {
      title: "Earthing Verification",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
      ),
      description: "Test and verify earthing systems to ensure effective protection against electrical faults and lightning."
    },
    {
      title: "Safety Compliance",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      description: "Complete tests required by international standards such as IEC 60364-6, NF C 15-100, and VDE 100."
    }
  ];

  return (
    <div className="py-12 bg-gradient-to-br from-yellow-50 to-white rounded-2xl my-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Application Areas</h2>
          <p className="text-lg text-gray-800 max-w-3xl mx-auto font-medium">
            Our installation testers are designed for a wide range of electrical testing applications
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {applications.map((app, idx) => (
            <motion.div
              key={idx}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: idx * 0.1 }}
              viewport={{ once: true }}
              className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-2 border-b-4 border-yellow-400"
            >
              <div className="bg-yellow-100 w-16 h-16 rounded-lg flex items-center justify-center mb-4 text-yellow-600">
                {app.icon}
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">{app.title}</h3>
              <p className="text-gray-800 font-medium">{app.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Contact Section Component with improved visibility
const ContactSection = ({ onContactClick }) => {
  return (
    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 py-4 md:py-6 px-3 rounded-xl shadow-lg font-['Open_Sans']">
      <div className="max-w-4xl mx-auto text-center">
        <h2 className="text-lg md:text-xl lg:text-2xl font-bold text-gray-900 mb-3 font-['Open_Sans']">Need More Information?</h2>
        <p className="text-gray-800 mb-4 md:mb-6 max-w-3xl mx-auto font-medium text-xs md:text-sm text-center font-['Open_Sans']">
          Our team of experts is ready to help you with product specifications, custom solutions,
          pricing, and any other details you need about KRYKARD Installation Testers.
        </p>
        <Button
          className="inline-flex items-center px-4 md:px-6 py-2 md:py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 transform hover:-translate-y-1 text-xs md:text-sm font-['Open_Sans']"
          onClick={onContactClick}
        >
          Contact Our Experts
          <ArrowRight className="ml-2 h-3 md:h-4 w-3 md:w-4" />
        </Button>
      </div>
    </div>
  );
};

// Main InstallationTesters Component
const InstallationTesters = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [activeProductType, setActiveProductType] = useState("ca6117");
  const [activeDetailTab, setActiveDetailTab] = useState("features");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    // For URL parameter handling (can be expanded)
    const params = new URLSearchParams(window.location.search);
    const tab = params.get('tab');

    if (tab) setActiveTab(tab);
  }, []);  // Handler for View Details button
  const handleViewDetails = (productType: string) => {
    // First set the active product type and tabs
    setActiveProductType(productType);
    setActiveTab('details');
    setActiveDetailTab('features');

    // Use requestAnimationFrame for better scroll handling
    requestAnimationFrame(() => {
      try {
        // Find a product-specific element to scroll to
        const productDetailsElement = document.querySelector('.product-details-section') ||
                                    document.getElementById('product-detail-section');
        if (productDetailsElement) {
          productDetailsElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        } else {
          // Fallback - scroll to top
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        }
      } catch (error) {
        console.error("Error during scroll:", error);
      }
    });
  };

  // Handler for Request Demo button
  const handleRequestDemo = () => {
    try {
      // Navigate to contact page or show a form modal
      window.location.href = "/contact/sales";
    } catch (error) {
      console.error("Navigation error:", error);
    }
  };

  // Handler for View Brochure button
  const handleViewBrochure = () => {
    try {
      window.open(PDF_URL, '_blank');
    } catch (error) {
      console.error("Error opening brochure:", error);
    }
  };

  // CA 6117 model data
  const ca6117Data = {
    title: "CA 6117",
    image: "/installation testers/CA 6117.png",
    displayInfo: "5.7\" backlit color LCD",
    features: [
      "All-in-one installation tester",
      "All neutral systems (TT, TN, IT)",
      "1000 memory locations",
    ],
    certifications: ["CE", "Standard", "Certification"],
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-100'
    }
  };

  // CA 6133 model data
  const ca6133Data = {
    title: "CA 6133",
    image: "/installation testers/CA 6133-1.png",
    displayInfo: "231-segment LCD with backlighting",
    features: [
      "Android app for report generation",
      "Automatic test sequences",
      "Multiple power supply options",
    ],
    certifications: ["CE", "Standard", "Certification"],
    colors: {
      primary: 'yellow-400',
      secondary: 'yellow-100'
    }
  };

  // Navigation tabs data
  const navTabs = [
    { id: "overview", label: "Overview", icon: <Gauge className="h-5 w-5" /> },
    { id: "details", label: "Product Details", icon: <Zap className="h-5 w-5" /> },
    { id: "applications", label: "Applications", icon: <Shield className="h-5 w-5" /> }
  ];

  return (
    <PageLayout
      title="INSTALLATION TESTERS"
      subtitle="Professional electrical test and measurement equipment"
      category="measure"
    >
      {/* Modern Background */}
      <ModernBackground />

      {/* Premium Modern Navigation Tabs - Responsive Design */}
      <div className="sticky top-20 sm:top-24 z-30 bg-white shadow-lg border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4">
          {/* Desktop Navigation */}
          <div className="hidden md:flex justify-center py-2">
            <div className="bg-gray-50 p-1.5 rounded-full flex shadow-sm">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    setIsMobileMenuOpen(false);
                  }}
                  className={cn(
                    "relative px-6 py-2.5 font-medium rounded-full transition-all duration-300 flex items-center mx-1 overflow-hidden",
                    activeTab === tab.id
                      ? "bg-gradient-to-r from-yellow-500 to-yellow-400 text-white shadow-md transform -translate-y-0.5"
                      : "text-gray-700 hover:bg-yellow-50 hover:text-yellow-500"
                  )}
                >
                  <div className="flex items-center relative z-10">
                    <span className="mr-2">{tab.icon}</span>
                    <span>{tab.label}</span>
                  </div>
                  {activeTab === tab.id && (
                    <motion.div
                      layoutId="navIndicator"
                      className="absolute inset-0 bg-gradient-to-r from-yellow-500 to-yellow-400 -z-0"
                      initial={false}
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden py-2 flex justify-between items-center">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-gray-700 hover:text-yellow-500 focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <span className="font-semibold text-gray-900 text-lg">
              {navTabs.find(tab => tab.id === activeTab)?.label}
            </span>

            <div className="w-6"></div> {/* Spacer for balanced layout */}
          </div>

          {/* Mobile Menu Dropdown */}
          <div className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-60' : 'max-h-0'}`}>
            <div className="bg-white rounded-lg shadow-lg p-2 mb-2">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    setIsMobileMenuOpen(false);
                  }}
                  className={`w-full text-left px-4 py-3 rounded-lg my-1 flex items-center ${
                    activeTab === tab.id
                      ? "bg-yellow-50 text-yellow-600 font-medium"
                      : "text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <span className="mr-3">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {activeTab === "overview" && (
        <>
          {/* Hero Section */}
          <HeroSection
            onRequestDemo={handleRequestDemo}
            onViewBrochure={handleViewBrochure}
          />

          {/* Key Features Overview - With clear separation from hero section */}
          <div className="py-6 md:py-8 mt-4 md:mt-6 bg-gradient-to-br from-yellow-50 to-white rounded-t-2xl relative">
            {/* Visual separator to create clear distinction between sections */}
            <div className="absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-yellow-200 via-yellow-300 to-yellow-200 rounded-t-2xl"></div>

            <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 pt-4">
              <div className="text-center mb-4 sm:mb-6 md:mb-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  viewport={{ once: true }}
                  className="pt-2 sm:pt-4"
                >
                  <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-yellow-400 mb-2 sm:mb-3 relative z-10 font-['Open_Sans']">Why Choose Our Installation Testers?</h2>
                  <p className="mt-2 text-xs sm:text-sm md:text-base text-gray-800 max-w-3xl mx-auto font-medium text-center font-['Open_Sans']">
                    Precision-engineered instruments that combine accuracy, durability, and advanced features for professional electrical testing
                  </p>
                </motion.div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6">
                <FeatureHighlight
                  icon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                  }
                  title="Compliance Assurance"
                  description="Our installation testers ensure compliance with international standards including IEC 60364-6, NF C 15-100, VDE 100, and FD C 16-600."
                />

                <FeatureHighlight
                  icon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                    </svg>
                  }
                  title="All Neutral Systems"
                  description="Compatible with all neutral system arrangements (TT, TN, IT), making them versatile for industries, tertiary and residential applications."
                />

                <FeatureHighlight
                  icon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                    </svg>
                  }
                  title="Comprehensive Testing"
                  description="Complete measurement capabilities including insulation, continuity, loop impedance, RCD testing, earth resistance, and more in a single instrument."
                />
              </div>
            </div>
          </div>

          {/* Products Section */}
          <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-6 sm:mb-8 md:mb-10"
            >
              <span className="inline-block bg-yellow-100 text-yellow-800 px-2 sm:px-3 py-1 rounded-full text-xs font-semibold mb-2 sm:mb-3 font-['Open_Sans']">
                PROFESSIONAL SERIES
              </span>
              <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 font-['Open_Sans']">
                Our Installation Tester Series
              </h2>
              <p className="max-w-3xl mx-auto text-gray-800 text-xs sm:text-sm md:text-base font-medium text-center font-['Open_Sans']">
                Choose the perfect tester for your electrical measurement and compliance needs.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 gap-3 sm:gap-4 md:gap-6">
              <ProductCard
                {...ca6117Data}
                onViewDetailsClick={() => handleViewDetails("ca6117")}
              />
              <ProductCard
                {...ca6133Data}
                onViewDetailsClick={() => handleViewDetails("ca6133")}
              />
            </div>
          </div>

          {/* Contact Information Section */}
          <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-3 sm:py-4 md:py-6">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </>
      )}

      {activeTab === "details" && (
        <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8">
          {/* Enhanced Product Type Selector */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-r from-yellow-50 to-white rounded-xl shadow-md p-3 sm:p-4 md:p-6 mb-4 sm:mb-6 relative overflow-hidden"
          >
            <div className="absolute top-0 right-0 w-64 h-64 bg-yellow-200 rounded-full opacity-20 transform translate-x-1/2 -translate-y-1/2 blur-3xl"></div>

            <h2 className="text-base sm:text-lg md:text-xl font-bold text-gray-900 mb-2 sm:mb-3 relative z-10 font-['Open_Sans']">
              Select <span className="text-yellow-400">Model</span>
            </h2>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3 relative z-10">
              <motion.button
                whileHover={{ y: -3, boxShadow: "0 8px 20px -5px rgba(0, 0, 0, 0.1)" }}
                whileTap={{ y: 0 }}
                onClick={() => {
                  setActiveProductType("ca6117");
                }}
                className={`relative rounded-lg transition-all duration-300 overflow-hidden ${
                  activeProductType === "ca6117"
                    ? "ring-2 ring-yellow-400"
                    : "hover:ring-1 hover:ring-yellow-300"
                }`}
              >
                <div className={`h-full py-3 sm:py-4 px-3 flex flex-col items-center text-center ${
                  activeProductType === "ca6117"
                    ? "bg-yellow-400 text-white"
                    : "bg-white hover:bg-yellow-50"
                }`}>
                  <div className="mb-2">
                    <Gauge className={`h-6 sm:h-8 w-6 sm:w-8 ${activeProductType === "ca6117" ? "text-white" : "text-yellow-400"}`} />
                  </div>

                  <h3 className={`text-base sm:text-lg font-bold mb-1 font-['Open_Sans'] ${activeProductType === "ca6117" ? "text-white" : "text-gray-900"}`}>
                    CA 6117
                  </h3>

                  <div className={`text-xs font-['Open_Sans'] ${activeProductType === "ca6117" ? "text-white opacity-80" : "text-gray-500"}`}>
                    Advanced Tester
                  </div>

                  {activeProductType === "ca6117" && (
                    <div className="mt-2 bg-white bg-opacity-20 rounded-full px-2 sm:px-3 py-0.5 text-xs font-semibold font-['Open_Sans']">
                      Selected
                    </div>
                  )}
                </div>
              </motion.button>

              <motion.button
                whileHover={{ y: -3, boxShadow: "0 8px 20px -5px rgba(0, 0, 0, 0.1)" }}
                whileTap={{ y: 0 }}
                onClick={() => {
                  setActiveProductType("ca6133");
                }}
                className={`relative rounded-lg transition-all duration-300 overflow-hidden ${
                  activeProductType === "ca6133"
                    ? "ring-2 ring-yellow-400"
                    : "hover:ring-1 hover:ring-yellow-300"
                }`}
              >
                <div className={`h-full py-3 sm:py-4 px-3 flex flex-col items-center text-center ${
                  activeProductType === "ca6133"
                    ? "bg-yellow-400 text-white"
                    : "bg-white hover:bg-yellow-50"
                }`}>
                  <div className="mb-2">
                    <Shield className={`h-6 sm:h-8 w-6 sm:w-8 ${activeProductType === "ca6133" ? "text-white" : "text-yellow-400"}`} />
                  </div>

                  <h3 className={`text-base sm:text-lg font-bold mb-1 font-['Open_Sans'] ${activeProductType === "ca6133" ? "text-white" : "text-gray-900"}`}>
                    CA 6133
                  </h3>

                  <div className={`text-xs font-['Open_Sans'] ${activeProductType === "ca6133" ? "text-white opacity-80" : "text-gray-500"}`}>
                    Standard Model
                  </div>

                  {activeProductType === "ca6133" && (
                    <div className="mt-2 bg-white bg-opacity-20 rounded-full px-2 sm:px-3 py-0.5 text-xs font-semibold font-['Open_Sans']">
                      Selected
                    </div>
                  )}
                </div>
              </motion.button>
            </div>
          </motion.div>          {/* Product Detail Section */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 sm:gap-6 md:gap-8 product-details-section">
            <div className="lg:col-span-5">
              <div className="sticky top-8 sm:top-16 lg:top-24">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="bg-gradient-to-br from-white to-yellow-50 rounded-xl shadow-lg p-4 sm:p-6 md:p-8 mb-4 sm:mb-6 relative overflow-hidden"
                >
                  {/* Background decoration */}
                  <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-yellow-200 rounded-full opacity-10 blur-3xl"></div>
                  <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-yellow-100 rounded-full opacity-10 blur-3xl"></div>

                  {/* Product badge */}
                  <div className="absolute top-3 sm:top-4 md:top-6 left-3 sm:left-4 md:left-6 bg-yellow-400 text-white px-2 sm:px-3 py-1 rounded-full text-xs font-semibold z-10 font-['Open_Sans']">
                    {activeProductType === "ca6117" ? "CA 6117" : "CA 6133"}
                  </div>

                  {/* Image container with glow effect */}
                  <div className="relative mb-4 sm:mb-6 md:mb-8 mt-3 sm:mt-4">
                    <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-30 blur-xl transform scale-90"></div>
                    <motion.div
                      animate={{ y: [0, -8, 0] }}
                      transition={{ repeat: Infinity, duration: 3, ease: "easeInOut" }}
                      className="relative z-10 flex justify-center items-center py-2 sm:py-3 md:py-4"
                    >
                      <img
                        src={activeProductType === "ca6117"
                          ? "/installation testers/CA 6117.png"
                          : "/installation testers/CA 6133-1.png"
                        }
                        alt={activeProductType === "ca6117" ? "CA 6117" : "CA 6133"}
                        className="max-h-48 sm:max-h-56 md:max-h-64 w-auto object-contain drop-shadow-2xl transform scale-100 transition-transform duration-500 hover:scale-110"
                      />
                    </motion.div>
                  </div>

                  {/* Product details */}
                  <div className="text-center mb-8">
                    <h3 className="text-4xl font-bold text-gray-900 mb-4">
                      {activeProductType === "ca6117" ? "CA 6117" : "CA 6133"}
                    </h3>
                    <div className="flex justify-center space-x-3 mb-6">
                      <span className="inline-block bg-yellow-100 text-yellow-700 px-4 py-2 rounded-full text-base font-semibold">
                        Installation Tester
                      </span>
                    </div>
                  </div>

                  {/* Specs cards */}
                  <div className="bg-white p-5 rounded-xl shadow-sm border border-yellow-100 transform transition-transform duration-300 hover:-translate-y-1 hover:shadow-md mb-6">
                    <div className="flex items-center mb-3">
                      <Gauge className="h-6 w-6 text-yellow-600 mr-3" />
                      <span className="text-base font-semibold text-gray-700">Display</span>
                    </div>
                    <span className="font-bold text-gray-900 text-xl">
                      {activeProductType === "ca6117" ? "5.7\" backlit color LCD" : "231-segment LCD with backlighting"}
                    </span>
                  </div>

                  {/* View Brochure button */}
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      className="w-full bg-gradient-to-r from-yellow-500 to-yellow-400 hover:from-yellow-600 hover:to-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition-all duration-300 flex items-center justify-center py-3"
                      onClick={handleViewBrochure}
                    >
                      <span>View Product Brochure</span>
                      <FileText className="ml-2 h-5 w-5" />
                    </Button>
                  </motion.div>
                </motion.div>
              </div>
            </div>

            <div className="md:col-span-7">
              {/* Enhanced Detail Tabs Navigation */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="bg-white rounded-xl shadow-lg mb-8 overflow-hidden"
              >
                <div className="flex border-b overflow-x-auto scrollbar-hide">
                  {[
                    { id: "features", label: "Features", icon: <Shield className="h-5 w-5" /> },
                    { id: "measurements", label: "Measurements", icon: <Gauge className="h-5 w-5" /> },
                    { id: "comparison", label: "Comparison", icon: <BarChart className="h-5 w-5" /> }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => {
                        setActiveDetailTab(tab.id);
                      }}
                      className={`px-6 py-4 font-semibold text-base whitespace-nowrap flex items-center transition-all duration-300 ${
                        activeDetailTab === tab.id
                          ? "bg-yellow-50 border-b-2 border-yellow-500 text-yellow-800"
                          : "text-gray-900 hover:text-yellow-700 hover:bg-yellow-50/50"
                      }`}
                    >
                      <span className="mr-2">{tab.icon}</span>
                      {tab.label}
                    </button>
                  ))}
                </div>
              </motion.div>

              {/* Tab Content */}
              <motion.div
                key={activeDetailTab}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="transform origin-top"
              >
                {activeDetailTab === "comparison" ? (
                  <ComparisonTable />
                ) : (
                  <FeaturesMeasurementsTab activeProductType={activeProductType} activeTab={activeDetailTab} />
                )}
              </motion.div>
            </div>
          </div>

          {/* Enhanced Contact Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className="mt-20"
          >
            <ContactSection onContactClick={handleRequestDemo} />
          </motion.div>
        </div>
      )}

      {activeTab === "applications" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h1 className="text-3xl font-bold text-gray-900 mb-4">Applications</h1>
              <p className="text-lg text-gray-800 max-w-3xl mx-auto font-medium">
                KRYKARD installation testers are versatile instruments designed for a wide range of professional applications
              </p>
            </motion.div>
          </div>

          <ApplicationsSection />

          <div className="mt-16 bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Industry-Specific Solutions</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Commercial Buildings</h3>
                  <p className="text-gray-800 font-medium">
                    Ensure electrical installations meet safety standards and compliance requirements in office buildings, shopping centers, and public facilities.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Industrial Facilities</h3>
                  <p className="text-gray-800 font-medium">
                    Test and verify electrical installations in factories, warehouses, and processing plants to maintain workplace safety and production efficiency.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Residential Buildings</h3>
                  <p className="text-gray-800 font-medium">
                    Verify electrical installations in homes, apartments, and residential complexes to ensure safety for occupants and compliance with local codes.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-yellow-100 p-3 rounded-lg text-yellow-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Electrical Contractors</h3>
                  <p className="text-gray-800 font-medium">
                    Essential tools for professional electricians to certify new installations, troubleshoot issues, and perform periodic testing and maintenance.
                  </p>
                </div>
              </motion.div>
            </div>

            {/* View Brochure button */}
            <div className="flex justify-center mt-8">
              <Button
                className="px-6 py-3 bg-gradient-to-r from-yellow-500 to-yellow-400 hover:from-yellow-600 hover:to-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center space-x-2"
                onClick={handleViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>

          <div className="mt-16">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </div>
      )}

      {/* No PDF Viewer Modal - Using direct link instead */}
    </PageLayout>
  );
};

export default InstallationTesters;